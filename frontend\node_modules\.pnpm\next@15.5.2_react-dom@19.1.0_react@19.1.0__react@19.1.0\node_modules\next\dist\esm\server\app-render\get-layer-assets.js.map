{"version": 3, "sources": ["../../../src/server/app-render/get-layer-assets.tsx"], "sourcesContent": ["import React from 'react'\nimport { getLinkAndScriptTags } from './get-css-inlined-link-tags'\nimport { getPreloadableFonts } from './get-preloadable-fonts'\nimport type { AppRenderContext } from './app-render'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { encodeURIPath } from '../../shared/lib/encode-uri-path'\nimport type { PreloadCallbacks } from './types'\nimport { renderCssResource } from './render-css-resource'\n\nexport function getLayerAssets({\n  ctx,\n  layoutOrPagePath,\n  injectedCSS: injectedCSSWithCurrentLayout,\n  injectedJS: injectedJSWithCurrentLayout,\n  injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n  preloadCallbacks,\n}: {\n  layoutOrPagePath: string | undefined\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  ctx: AppRenderContext\n  preloadCallbacks: PreloadCallbacks\n}): React.ReactNode {\n  const { styles: styleTags, scripts: scriptTags } = layoutOrPagePath\n    ? getLinkAndScriptTags(\n        ctx.clientReferenceManifest,\n        layoutOrPagePath,\n        injectedCSSWithCurrentLayout,\n        injectedJSWithCurrentLayout,\n        true\n      )\n    : { styles: [], scripts: [] }\n\n  const preloadedFontFiles = layoutOrPagePath\n    ? getPreloadableFonts(\n        ctx.renderOpts.nextFontManifest,\n        layoutOrPagePath,\n        injectedFontPreloadTagsWithCurrentLayout\n      )\n    : null\n\n  if (preloadedFontFiles) {\n    if (preloadedFontFiles.length) {\n      for (let i = 0; i < preloadedFontFiles.length; i++) {\n        const fontFilename = preloadedFontFiles[i]\n        const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFilename)![1]\n        const type = `font/${ext}`\n        const href = `${ctx.assetPrefix}/_next/${encodeURIPath(fontFilename)}`\n\n        preloadCallbacks.push(() => {\n          ctx.componentMod.preloadFont(\n            href,\n            type,\n            ctx.renderOpts.crossOrigin,\n            ctx.nonce\n          )\n        })\n      }\n    } else {\n      try {\n        let url = new URL(ctx.assetPrefix)\n        preloadCallbacks.push(() => {\n          ctx.componentMod.preconnect(url.origin, 'anonymous', ctx.nonce)\n        })\n      } catch (error) {\n        // assetPrefix must not be a fully qualified domain name. We assume\n        // we should preconnect to same origin instead\n        preloadCallbacks.push(() => {\n          ctx.componentMod.preconnect('/', 'anonymous', ctx.nonce)\n        })\n      }\n    }\n  }\n\n  const styles = renderCssResource(styleTags, ctx, preloadCallbacks)\n\n  const scripts = scriptTags\n    ? scriptTags.map((href, index) => {\n        const fullSrc = `${ctx.assetPrefix}/_next/${encodeURIPath(\n          href\n        )}${getAssetQueryString(ctx, true)}`\n\n        return (\n          <script\n            src={fullSrc}\n            async={true}\n            key={`script-${index}`}\n            nonce={ctx.nonce}\n          />\n        )\n      })\n    : []\n\n  return styles.length || scripts.length ? [...styles, ...scripts] : null\n}\n"], "names": ["React", "getLinkAndScriptTags", "getPreloadableFonts", "getAssetQueryString", "encodeURIPath", "renderCssResource", "getLayerAssets", "ctx", "layoutOrPagePath", "injectedCSS", "injectedCSSWithCurrentLayout", "injectedJS", "injectedJSWithCurrentLayout", "injectedFontPreloadTags", "injectedFontPreloadTagsWithCurrentLayout", "preloadCallbacks", "styles", "styleTags", "scripts", "scriptTags", "clientReferenceManifest", "preloadedFontFiles", "renderOpts", "nextFontManifest", "length", "i", "fontFilename", "ext", "exec", "type", "href", "assetPrefix", "push", "componentMod", "preloadFont", "crossOrigin", "nonce", "url", "URL", "preconnect", "origin", "error", "map", "index", "fullSrc", "script", "src", "async"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,oBAAoB,QAAQ,8BAA6B;AAClE,SAASC,mBAAmB,QAAQ,0BAAyB;AAE7D,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,aAAa,QAAQ,mCAAkC;AAEhE,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD,OAAO,SAASC,eAAe,EAC7BC,GAAG,EACHC,gBAAgB,EAChBC,aAAaC,4BAA4B,EACzCC,YAAYC,2BAA2B,EACvCC,yBAAyBC,wCAAwC,EACjEC,gBAAgB,EAQjB;IACC,MAAM,EAAEC,QAAQC,SAAS,EAAEC,SAASC,UAAU,EAAE,GAAGX,mBAC/CP,qBACEM,IAAIa,uBAAuB,EAC3BZ,kBACAE,8BACAE,6BACA,QAEF;QAAEI,QAAQ,EAAE;QAAEE,SAAS,EAAE;IAAC;IAE9B,MAAMG,qBAAqBb,mBACvBN,oBACEK,IAAIe,UAAU,CAACC,gBAAgB,EAC/Bf,kBACAM,4CAEF;IAEJ,IAAIO,oBAAoB;QACtB,IAAIA,mBAAmBG,MAAM,EAAE;YAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,mBAAmBG,MAAM,EAAEC,IAAK;gBAClD,MAAMC,eAAeL,kBAAkB,CAACI,EAAE;gBAC1C,MAAME,MAAM,8BAA8BC,IAAI,CAACF,aAAc,CAAC,EAAE;gBAChE,MAAMG,OAAO,CAAC,KAAK,EAAEF,KAAK;gBAC1B,MAAMG,OAAO,GAAGvB,IAAIwB,WAAW,CAAC,OAAO,EAAE3B,cAAcsB,eAAe;gBAEtEX,iBAAiBiB,IAAI,CAAC;oBACpBzB,IAAI0B,YAAY,CAACC,WAAW,CAC1BJ,MACAD,MACAtB,IAAIe,UAAU,CAACa,WAAW,EAC1B5B,IAAI6B,KAAK;gBAEb;YACF;QACF,OAAO;YACL,IAAI;gBACF,IAAIC,MAAM,IAAIC,IAAI/B,IAAIwB,WAAW;gBACjChB,iBAAiBiB,IAAI,CAAC;oBACpBzB,IAAI0B,YAAY,CAACM,UAAU,CAACF,IAAIG,MAAM,EAAE,aAAajC,IAAI6B,KAAK;gBAChE;YACF,EAAE,OAAOK,OAAO;gBACd,mEAAmE;gBACnE,8CAA8C;gBAC9C1B,iBAAiBiB,IAAI,CAAC;oBACpBzB,IAAI0B,YAAY,CAACM,UAAU,CAAC,KAAK,aAAahC,IAAI6B,KAAK;gBACzD;YACF;QACF;IACF;IAEA,MAAMpB,SAASX,kBAAkBY,WAAWV,KAAKQ;IAEjD,MAAMG,UAAUC,aACZA,WAAWuB,GAAG,CAAC,CAACZ,MAAMa;QACpB,MAAMC,UAAU,GAAGrC,IAAIwB,WAAW,CAAC,OAAO,EAAE3B,cAC1C0B,QACE3B,oBAAoBI,KAAK,OAAO;QAEpC,qBACE,KAACsC;YACCC,KAAKF;YACLG,OAAO;YAEPX,OAAO7B,IAAI6B,KAAK;WADX,CAAC,OAAO,EAAEO,OAAO;IAI5B,KACA,EAAE;IAEN,OAAO3B,OAAOQ,MAAM,IAAIN,QAAQM,MAAM,GAAG;WAAIR;WAAWE;KAAQ,GAAG;AACrE", "ignoreList": [0]}