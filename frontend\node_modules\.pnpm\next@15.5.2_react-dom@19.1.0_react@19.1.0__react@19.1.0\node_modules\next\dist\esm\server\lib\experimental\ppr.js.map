{"version": 3, "sources": ["../../../../src/server/lib/experimental/ppr.ts"], "sourcesContent": ["/**\n * If set to `incremental`, only those leaf pages that export\n * `experimental_ppr = true` will have partial prerendering enabled. If any\n * page exports this value as `false` or does not export it at all will not\n * have partial prerendering enabled. If set to a boolean, the options for\n * `experimental_ppr` will be ignored.\n */\n\nexport type ExperimentalPPRConfig = boolean | 'incremental'\n\n/**\n * Returns true if partial prerendering is enabled for the application. It does\n * not tell you if a given route has PPR enabled, as that requires analysis of\n * the route's configuration.\n *\n * @see {@link checkIsRoutePPREnabled} - for checking if a specific route has PPR enabled.\n */\nexport function checkIsAppPPREnabled(\n  config: ExperimentalPPRConfig | undefined\n): boolean {\n  // If the config is undefined, partial prerendering is disabled.\n  if (typeof config === 'undefined') return false\n\n  // If the config is a boolean, use it directly.\n  if (typeof config === 'boolean') return config\n\n  // If the config is a string, it must be 'incremental' to enable partial\n  // prerendering.\n  if (config === 'incremental') return true\n\n  return false\n}\n\n/**\n * Returns true if partial prerendering is supported for the current page with\n * the provided app configuration. If the application doesn't have partial\n * prerendering enabled, this function will always return false. If you want to\n * check if the application has partial prerendering enabled\n *\n * @see {@link checkIsAppPPREnabled} for checking if the application has PPR enabled.\n */\nexport function checkIsRoutePPREnabled(\n  config: ExperimentalPPRConfig | undefined,\n  appConfig: {\n    experimental_ppr?: boolean\n  }\n): boolean {\n  // If the config is undefined, partial prerendering is disabled.\n  if (typeof config === 'undefined') return false\n\n  // If the config is a boolean, use it directly.\n  if (typeof config === 'boolean') return config\n\n  // If the config is a string, it must be 'incremental' to enable partial\n  // prerendering.\n  if (config === 'incremental' && appConfig.experimental_ppr === true) {\n    return true\n  }\n\n  return false\n}\n"], "names": ["checkIsAppPPREnabled", "config", "checkIsRoutePPREnabled", "appConfig", "experimental_ppr"], "mappings": "AAAA;;;;;;CAMC,GAID;;;;;;CAMC,GACD,OAAO,SAASA,qBACdC,MAAyC;IAEzC,gEAAgE;IAChE,IAAI,OAAOA,WAAW,aAAa,OAAO;IAE1C,+CAA+C;IAC/C,IAAI,OAAOA,WAAW,WAAW,OAAOA;IAExC,wEAAwE;IACxE,gBAAgB;IAChB,IAAIA,WAAW,eAAe,OAAO;IAErC,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,OAAO,SAASC,uBACdD,MAAyC,EACzCE,SAEC;IAED,gEAAgE;IAChE,IAAI,OAAOF,WAAW,aAAa,OAAO;IAE1C,+CAA+C;IAC/C,IAAI,OAAOA,WAAW,WAAW,OAAOA;IAExC,wEAAwE;IACxE,gBAAgB;IAChB,IAAIA,WAAW,iBAAiBE,UAAUC,gBAAgB,KAAK,MAAM;QACnE,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0]}