{"version": 3, "sources": ["../../../src/server/lib/fix-mojibake.ts"], "sourcesContent": ["// x-matched-path header can be decoded incorrectly\n// and should only be utf8 characters so this fixes\n// incorrectly encoded values\nexport function fixM<PERSON><PERSON><PERSON><PERSON>(input: string): string {\n  // Convert each character's char code to a byte\n  const bytes = new Uint8Array(input.length)\n  for (let i = 0; i < input.length; i++) {\n    bytes[i] = input.charCodeAt(i)\n  }\n\n  // Decode the bytes as proper UTF-8\n  const decoder = new TextDecoder('utf-8')\n  return decoder.decode(bytes)\n}\n"], "names": ["fixMojibake", "input", "bytes", "Uint8Array", "length", "i", "charCodeAt", "decoder", "TextDecoder", "decode"], "mappings": "AAAA,mDAAmD;AACnD,mDAAmD;AACnD,6BAA6B;AAC7B,OAAO,SAASA,YAAYC,KAAa;IACvC,+CAA+C;IAC/C,MAAMC,QAAQ,IAAIC,WAAWF,MAAMG,MAAM;IACzC,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,MAAMG,MAAM,EAAEC,IAAK;QACrCH,KAAK,CAACG,EAAE,GAAGJ,MAAMK,UAAU,CAACD;IAC9B;IAEA,mCAAmC;IACnC,MAAME,UAAU,IAAIC,YAAY;IAChC,OAAOD,QAAQE,MAAM,CAACP;AACxB", "ignoreList": [0]}