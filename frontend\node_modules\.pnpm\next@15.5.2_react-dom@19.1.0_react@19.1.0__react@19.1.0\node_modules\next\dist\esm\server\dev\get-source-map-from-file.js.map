{"version": 3, "sources": ["../../../src/server/dev/get-source-map-from-file.ts"], "sourcesContent": ["import fs from 'fs/promises'\nimport path from 'path'\nimport url from 'url'\nimport type { RawSourceMap } from 'next/dist/compiled/source-map08'\nimport dataUriToBuffer from 'next/dist/compiled/data-uri-to-buffer'\n\nfunction getSourceMapUrl(fileContents: string): string | null {\n  const regex = /\\/\\/[#@] ?sourceMappingURL=([^\\s'\"]+)\\s*$/gm\n  let match = null\n  for (;;) {\n    let next = regex.exec(fileContents)\n    if (next == null) {\n      break\n    }\n    match = next\n  }\n  if (!(match && match[1])) {\n    return null\n  }\n  return match[1].toString()\n}\n\nexport async function getSourceMapFromFile(\n  filename: string\n): Promise<RawSourceMap | undefined> {\n  filename = filename.startsWith('file://')\n    ? url.fileURLToPath(filename)\n    : filename\n\n  let fileContents: string\n\n  try {\n    fileContents = await fs.readFile(filename, 'utf-8')\n  } catch (error) {\n    throw new Error(`Failed to read file contents of ${filename}.`, {\n      cause: error,\n    })\n  }\n\n  const sourceUrl = getSourceMapUrl(fileContents)\n\n  if (!sourceUrl) {\n    return undefined\n  }\n\n  if (sourceUrl.startsWith('data:')) {\n    let buffer: dataUriToBuffer.MimeBuffer\n\n    try {\n      buffer = dataUriToBuffer(sourceUrl)\n    } catch (error) {\n      throw new Error(`Failed to parse source map URL for ${filename}.`, {\n        cause: error,\n      })\n    }\n\n    if (buffer.type !== 'application/json') {\n      throw new Error(\n        `Unknown source map type for ${filename}: ${buffer.typeFull}.`\n      )\n    }\n\n    try {\n      return JSON.parse(buffer.toString())\n    } catch (error) {\n      throw new Error(`Failed to parse source map for ${filename}.`, {\n        cause: error,\n      })\n    }\n  }\n\n  const sourceMapFilename = path.resolve(\n    path.dirname(filename),\n    decodeURIComponent(sourceUrl)\n  )\n\n  try {\n    const sourceMapContents = await fs.readFile(sourceMapFilename, 'utf-8')\n\n    return JSON.parse(sourceMapContents.toString())\n  } catch (error) {\n    throw new Error(`Failed to parse source map ${sourceMapFilename}.`, {\n      cause: error,\n    })\n  }\n}\n"], "names": ["fs", "path", "url", "dataUriToBuffer", "getSourceMapUrl", "fileContents", "regex", "match", "next", "exec", "toString", "getSourceMapFromFile", "filename", "startsWith", "fileURLToPath", "readFile", "error", "Error", "cause", "sourceUrl", "undefined", "buffer", "type", "typeFull", "JSON", "parse", "sourceMapFilename", "resolve", "dirname", "decodeURIComponent", "sourceMapContents"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AACvB,OAAOC,SAAS,MAAK;AAErB,OAAOC,qBAAqB,wCAAuC;AAEnE,SAASC,gBAAgBC,YAAoB;IAC3C,MAAMC,QAAQ;IACd,IAAIC,QAAQ;IACZ,OAAS;QACP,IAAIC,OAAOF,MAAMG,IAAI,CAACJ;QACtB,IAAIG,QAAQ,MAAM;YAChB;QACF;QACAD,QAAQC;IACV;IACA,IAAI,CAAED,CAAAA,SAASA,KAAK,CAAC,EAAE,AAAD,GAAI;QACxB,OAAO;IACT;IACA,OAAOA,KAAK,CAAC,EAAE,CAACG,QAAQ;AAC1B;AAEA,OAAO,eAAeC,qBACpBC,QAAgB;IAEhBA,WAAWA,SAASC,UAAU,CAAC,aAC3BX,IAAIY,aAAa,CAACF,YAClBA;IAEJ,IAAIP;IAEJ,IAAI;QACFA,eAAe,MAAML,GAAGe,QAAQ,CAACH,UAAU;IAC7C,EAAE,OAAOI,OAAO;QACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,CAAC,gCAAgC,EAAEL,SAAS,CAAC,CAAC,EAAE;YAC9DM,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;IAEA,MAAMG,YAAYf,gBAAgBC;IAElC,IAAI,CAACc,WAAW;QACd,OAAOC;IACT;IAEA,IAAID,UAAUN,UAAU,CAAC,UAAU;QACjC,IAAIQ;QAEJ,IAAI;YACFA,SAASlB,gBAAgBgB;QAC3B,EAAE,OAAOH,OAAO;YACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,CAAC,mCAAmC,EAAEL,SAAS,CAAC,CAAC,EAAE;gBACjEM,OAAOF;YACT,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;QAEA,IAAIK,OAAOC,IAAI,KAAK,oBAAoB;YACtC,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,4BAA4B,EAAEL,SAAS,EAAE,EAAES,OAAOE,QAAQ,CAAC,CAAC,CAAC,GAD1D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI;YACF,OAAOC,KAAKC,KAAK,CAACJ,OAAOX,QAAQ;QACnC,EAAE,OAAOM,OAAO;YACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,CAAC,+BAA+B,EAAEL,SAAS,CAAC,CAAC,EAAE;gBAC7DM,OAAOF;YACT,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;IACF;IAEA,MAAMU,oBAAoBzB,KAAK0B,OAAO,CACpC1B,KAAK2B,OAAO,CAAChB,WACbiB,mBAAmBV;IAGrB,IAAI;QACF,MAAMW,oBAAoB,MAAM9B,GAAGe,QAAQ,CAACW,mBAAmB;QAE/D,OAAOF,KAAKC,KAAK,CAACK,kBAAkBpB,QAAQ;IAC9C,EAAE,OAAOM,OAAO;QACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,CAAC,2BAA2B,EAAES,kBAAkB,CAAC,CAAC,EAAE;YAClER,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;AACF", "ignoreList": [0]}