{"version": 3, "sources": ["../../../src/server/app-render/get-asset-query-string.ts"], "sourcesContent": ["import type { AppRenderContext } from './app-render'\n\nconst isDev = process.env.NODE_ENV === 'development'\nconst isTurbopack = !!process.env.TURBOPACK\n\nexport function getAssetQueryString(\n  ctx: AppRenderContext,\n  addTimestamp: boolean\n) {\n  let qs = ''\n\n  // In development we add the request timestamp to allow react to\n  // reload assets when a new RSC response is received.\n  // Turbopack handles HMR of assets itself and react doesn't need to reload them\n  // so this approach is not needed for Turbopack.\n  const shouldAddVersion = isDev && !isTurbopack && addTimestamp\n  if (shouldAddVersion) {\n    qs += `?v=${ctx.requestTimestamp}`\n  }\n\n  if (ctx.renderOpts.deploymentId) {\n    qs += `${shouldAddVersion ? '&' : '?'}dpl=${ctx.renderOpts.deploymentId}`\n  }\n  return qs\n}\n"], "names": ["isDev", "process", "env", "NODE_ENV", "isTurbopack", "TURBOPACK", "getAssetQueryString", "ctx", "addTimestamp", "qs", "shouldAddVersion", "requestTimestamp", "renderOpts", "deploymentId"], "mappings": "AAEA,MAAMA,QAAQC,QAAQC,GAAG,CAACC,QAAQ,KAAK;AACvC,MAAMC,cAAc,CAAC,CAACH,QAAQC,GAAG,CAACG,SAAS;AAE3C,OAAO,SAASC,oBACdC,GAAqB,EACrBC,YAAqB;IAErB,IAAIC,KAAK;IAET,gEAAgE;IAChE,qDAAqD;IACrD,+EAA+E;IAC/E,gDAAgD;IAChD,MAAMC,mBAAmBV,SAAS,CAACI,eAAeI;IAClD,IAAIE,kBAAkB;QACpBD,MAAM,CAAC,GAAG,EAAEF,IAAII,gBAAgB,EAAE;IACpC;IAEA,IAAIJ,IAAIK,UAAU,CAACC,YAAY,EAAE;QAC/BJ,MAAM,GAAGC,mBAAmB,MAAM,IAAI,IAAI,EAAEH,IAAIK,UAAU,CAACC,YAAY,EAAE;IAC3E;IACA,OAAOJ;AACT", "ignoreList": [0]}