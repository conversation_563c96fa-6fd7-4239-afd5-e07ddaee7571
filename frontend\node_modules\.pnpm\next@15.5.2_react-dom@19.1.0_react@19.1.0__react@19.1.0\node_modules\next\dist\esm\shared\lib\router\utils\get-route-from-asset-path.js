// Translate a pages asset path (relative from a common prefix) back into its logical route
import { isDynamicRoute } from './is-dynamic';
// "asset path" being its javascript file, data file, prerendered html,...
export default function getRouteFromAssetPath(assetPath, ext) {
    if (ext === void 0) ext = '';
    assetPath = assetPath.replace(/\\/g, '/');
    assetPath = ext && assetPath.endsWith(ext) ? assetPath.slice(0, -ext.length) : assetPath;
    if (assetPath.startsWith('/index/') && !isDynamicRoute(assetPath)) {
        assetPath = assetPath.slice(6);
    } else if (assetPath === '/index') {
        assetPath = '/';
    }
    return assetPath;
}

//# sourceMappingURL=get-route-from-asset-path.js.map