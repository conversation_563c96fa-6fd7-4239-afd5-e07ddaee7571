{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/memory-cache.external.ts"], "sourcesContent": ["import type { <PERSON>ache<PERSON>andlerValue } from '.'\nimport { CachedRouteKind } from '../../response-cache/types'\nimport { LRUCache } from '../lru-cache'\n\nlet memoryCache: LRUCache<CacheHandlerValue> | undefined\n\nexport function getMemoryCache(maxMemoryCacheSize: number) {\n  if (!memoryCache) {\n    memoryCache = new LRUCache(maxMemoryCacheSize, function length({ value }) {\n      if (!value) {\n        return 25\n      } else if (value.kind === CachedRouteKind.REDIRECT) {\n        return JSON.stringify(value.props).length\n      } else if (value.kind === CachedRouteKind.IMAGE) {\n        throw new Error('invariant image should not be incremental-cache')\n      } else if (value.kind === CachedRouteKind.FETCH) {\n        return JSON.stringify(value.data || '').length\n      } else if (value.kind === CachedRouteKind.APP_ROUTE) {\n        return value.body.length\n      }\n      // rough estimate of size of cache value\n      return (\n        value.html.length +\n        (JSON.stringify(\n          value.kind === CachedRouteKind.APP_PAGE\n            ? value.rscData\n            : value.pageData\n        )?.length || 0)\n      )\n    })\n  }\n\n  return memoryCache\n}\n"], "names": ["CachedRouteKind", "L<PERSON><PERSON><PERSON>", "memoryCache", "getMemoryCache", "maxMemoryCacheSize", "length", "value", "JSON", "kind", "REDIRECT", "stringify", "props", "IMAGE", "Error", "FETCH", "data", "APP_ROUTE", "body", "html", "APP_PAGE", "rscData", "pageData"], "mappings": "AACA,SAASA,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,QAAQ,QAAQ,eAAc;AAEvC,IAAIC;AAEJ,OAAO,SAASC,eAAeC,kBAA0B;IACvD,IAAI,CAACF,aAAa;QAChBA,cAAc,IAAID,SAASG,oBAAoB,SAASC,OAAO,EAAEC,KAAK,EAAE;gBAenEC;YAdH,IAAI,CAACD,OAAO;gBACV,OAAO;YACT,OAAO,IAAIA,MAAME,IAAI,KAAKR,gBAAgBS,QAAQ,EAAE;gBAClD,OAAOF,KAAKG,SAAS,CAACJ,MAAMK,KAAK,EAAEN,MAAM;YAC3C,OAAO,IAAIC,MAAME,IAAI,KAAKR,gBAAgBY,KAAK,EAAE;gBAC/C,MAAM,qBAA4D,CAA5D,IAAIC,MAAM,oDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA2D;YACnE,OAAO,IAAIP,MAAME,IAAI,KAAKR,gBAAgBc,KAAK,EAAE;gBAC/C,OAAOP,KAAKG,SAAS,CAACJ,MAAMS,IAAI,IAAI,IAAIV,MAAM;YAChD,OAAO,IAAIC,MAAME,IAAI,KAAKR,gBAAgBgB,SAAS,EAAE;gBACnD,OAAOV,MAAMW,IAAI,CAACZ,MAAM;YAC1B;YACA,wCAAwC;YACxC,OACEC,MAAMY,IAAI,CAACb,MAAM,GAChBE,CAAAA,EAAAA,kBAAAA,KAAKG,SAAS,CACbJ,MAAME,IAAI,KAAKR,gBAAgBmB,QAAQ,GACnCb,MAAMc,OAAO,GACbd,MAAMe,QAAQ,sBAHnBd,gBAIEF,MAAM,KAAI,CAAA;QAEjB;IACF;IAEA,OAAOH;AACT", "ignoreList": [0]}