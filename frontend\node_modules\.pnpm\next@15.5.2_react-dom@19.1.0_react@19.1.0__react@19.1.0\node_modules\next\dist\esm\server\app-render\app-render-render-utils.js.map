{"version": 3, "sources": ["../../../src/server/app-render/app-render-render-utils.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\n\n/**\n * This is a utility function to make scheduling sequential tasks that run back to back easier.\n * We schedule on the same queue (setImmediate) at the same time to ensure no other events can sneak in between.\n */\nexport function scheduleInSequentialTasks<R>(\n  render: () => R | Promise<R>,\n  followup: () => void\n): Promise<R> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`scheduleInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: R | Promise<R>\n      setImmediate(() => {\n        try {\n          pendingResult = render()\n        } catch (err) {\n          reject(err)\n        }\n      })\n      setImmediate(() => {\n        followup()\n        resolve(pendingResult)\n      })\n    })\n  }\n}\n"], "names": ["InvariantError", "scheduleInSequentialTasks", "render", "followup", "process", "env", "NEXT_RUNTIME", "Promise", "resolve", "reject", "pendingResult", "setImmediate", "err"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mCAAkC;AAEjE;;;CAGC,GACD,OAAO,SAASC,0BACdC,MAA4B,EAC5BC,QAAoB;IAEpB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIN,eACR,sEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIO,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,aAAa;gBACX,IAAI;oBACFD,gBAAgBR;gBAClB,EAAE,OAAOU,KAAK;oBACZH,OAAOG;gBACT;YACF;YACAD,aAAa;gBACXR;gBACAK,QAAQE;YACV;QACF;IACF;AACF", "ignoreList": [0]}