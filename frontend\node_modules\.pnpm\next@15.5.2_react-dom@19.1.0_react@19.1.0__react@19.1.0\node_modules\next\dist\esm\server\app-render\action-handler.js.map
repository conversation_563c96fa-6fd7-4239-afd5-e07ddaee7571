{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'node:http'\nimport type { SizeLimit } from '../../types'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { AppRenderContext, GenerateFlight } from './app-render'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\n\nimport {\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  ACTION_HEADER,\n  NEXT_ACTION_NOT_FOUND_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_URL,\n} from '../../client/components/app-router-headers'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getRedirectTypeFromError,\n  getURLFromRedirectError,\n} from '../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectType,\n} from '../../client/components/redirect-error'\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n} from '../render-result'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  filterReqHeaders,\n  actionsForbiddenHeaders,\n} from '../lib/server-ipc/utils'\nimport { getModifiedCookieValues } from '../web/spec-extension/adapters/request-cookies'\n\nimport {\n  JSON_CONTENT_TYPE_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n} from '../../lib/constants'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { isCsrfOriginAllowed } from './csrf-protection'\nimport { warn } from '../../build/output/log'\nimport { RequestCookies, ResponseCookies } from '../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport { fromNodeOutgoingHttpHeaders } from '../web/utils'\nimport { selectWorkerForForwarding } from './action-utils'\nimport { isNodeNextRequest, isWebNextRequest } from '../base-http/helpers'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { synchronizeMutableCookies } from '../async-storage/request-store'\nimport type { TemporaryReferenceSet } from 'react-server-dom-webpack/server'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { executeRevalidates } from '../revalidation-utils'\nimport { getRequestMeta } from '../request-meta'\nimport { setCacheBustingSearchParam } from '../../client/components/router-reducer/set-cache-busting-search-param'\n\nfunction formDataFromSearchQueryString(query: string) {\n  const searchParams = new URLSearchParams(query)\n  const formData = new FormData()\n  for (const [key, value] of searchParams) {\n    formData.append(key, value)\n  }\n  return formData\n}\n\nfunction nodeHeadersToRecord(\n  headers: IncomingHttpHeaders | OutgoingHttpHeaders\n) {\n  const record: Record<string, string> = {}\n  for (const [key, value] of Object.entries(headers)) {\n    if (value !== undefined) {\n      record[key] = Array.isArray(value) ? value.join(', ') : `${value}`\n    }\n  }\n  return record\n}\n\nfunction getForwardedHeaders(\n  req: BaseNextRequest,\n  res: BaseNextResponse\n): Headers {\n  // Get request headers and cookies\n  const requestHeaders = req.headers\n  const requestCookies = new RequestCookies(HeadersAdapter.from(requestHeaders))\n\n  // Get response headers and cookies\n  const responseHeaders = res.getHeaders()\n  const responseCookies = new ResponseCookies(\n    fromNodeOutgoingHttpHeaders(responseHeaders)\n  )\n\n  // Merge request and response headers\n  const mergedHeaders = filterReqHeaders(\n    {\n      ...nodeHeadersToRecord(requestHeaders),\n      ...nodeHeadersToRecord(responseHeaders),\n    },\n    actionsForbiddenHeaders\n  ) as Record<string, string>\n\n  // Merge cookies into requestCookies, so responseCookies always take precedence\n  // and overwrite/delete those from requestCookies.\n  responseCookies.getAll().forEach((cookie) => {\n    if (typeof cookie.value === 'undefined') {\n      requestCookies.delete(cookie.name)\n    } else {\n      requestCookies.set(cookie)\n    }\n  })\n\n  // Update the 'cookie' header with the merged cookies\n  mergedHeaders['cookie'] = requestCookies.toString()\n\n  // Remove headers that should not be forwarded\n  delete mergedHeaders['transfer-encoding']\n\n  return new Headers(mergedHeaders)\n}\n\nfunction addRevalidationHeader(\n  res: BaseNextResponse,\n  {\n    workStore,\n    requestStore,\n  }: {\n    workStore: WorkStore\n    requestStore: RequestStore\n  }\n) {\n  // If a tag was revalidated, the client router needs to invalidate all the\n  // client router cache as they may be stale. And if a path was revalidated, the\n  // client needs to invalidate all subtrees below that path.\n\n  // To keep the header size small, we use a tuple of\n  // [[revalidatedPaths], isTagRevalidated ? 1 : 0, isCookieRevalidated ? 1 : 0]\n  // instead of a JSON object.\n\n  // TODO-APP: Currently the prefetch cache doesn't have subtree information,\n  // so we need to invalidate the entire cache if a path was revalidated.\n  // TODO-APP: Currently paths are treated as tags, so the second element of the tuple\n  // is always empty.\n\n  const isTagRevalidated = workStore.pendingRevalidatedTags?.length ? 1 : 0\n  const isCookieRevalidated = getModifiedCookieValues(\n    requestStore.mutableCookies\n  ).length\n    ? 1\n    : 0\n\n  res.setHeader(\n    'x-action-revalidated',\n    JSON.stringify([[], isTagRevalidated, isCookieRevalidated])\n  )\n}\n\n/**\n * Forwards a server action request to a separate worker. Used when the requested action is not available in the current worker.\n */\nasync function createForwardedActionResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  host: Host,\n  workerPathname: string,\n  basePath: string\n) {\n  if (!host) {\n    throw new Error(\n      'Invariant: Missing `host` header from a forwarded Server Actions request.'\n    )\n  }\n\n  const forwardedHeaders = getForwardedHeaders(req, res)\n\n  // indicate that this action request was forwarded from another worker\n  // we use this to skip rendering the flight tree so that we don't update the UI\n  // with the response from the forwarded worker\n  forwardedHeaders.set('x-action-forwarded', '1')\n\n  const proto =\n    getRequestMeta(req, 'initProtocol')?.replace(/:+$/, '') || 'https'\n\n  // For standalone or the serverful mode, use the internal origin directly\n  // other than the host headers from the request.\n  const origin = process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${host.value}`\n\n  const fetchUrl = new URL(`${origin}${basePath}${workerPathname}`)\n\n  try {\n    let body: BodyInit | ReadableStream<Uint8Array> | undefined\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(req)\n    ) {\n      if (!req.body) {\n        throw new Error('Invariant: missing request body.')\n      }\n\n      body = req.body\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req)\n    ) {\n      body = req.stream()\n    } else {\n      throw new Error('Invariant: Unknown request type.')\n    }\n\n    // Forward the request to the new worker\n    const response = await fetch(fetchUrl, {\n      method: 'POST',\n      body,\n      duplex: 'half',\n      headers: forwardedHeaders,\n      redirect: 'manual',\n      next: {\n        // @ts-ignore\n        internal: 1,\n      },\n    })\n\n    if (\n      response.headers.get('content-type')?.startsWith(RSC_CONTENT_TYPE_HEADER)\n    ) {\n      // copy the headers from the redirect response to the response we're sending\n      for (const [key, value] of response.headers) {\n        if (!actionsForbiddenHeaders.includes(key)) {\n          res.setHeader(key, value)\n        }\n      }\n\n      return new FlightRenderResult(response.body!)\n    } else {\n      // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n      response.body?.cancel()\n    }\n  } catch (err) {\n    // we couldn't stream the forwarded response, so we'll just return an empty response\n    console.error(`failed to forward action response`, err)\n  }\n\n  return RenderResult.fromStatic('{}', JSON_CONTENT_TYPE_HEADER)\n}\n\n/**\n * Returns the parsed redirect URL if we deem that it is hosted by us.\n *\n * We handle both relative and absolute redirect URLs.\n *\n * In case the redirect URL is not relative to the application we return `null`.\n */\nfunction getAppRelativeRedirectUrl(\n  basePath: string,\n  host: Host,\n  redirectUrl: string\n): URL | null {\n  if (redirectUrl.startsWith('/') || redirectUrl.startsWith('.')) {\n    // Make sure we are appending the basePath to relative URLS\n    return new URL(`${basePath}${redirectUrl}`, 'http://n')\n  }\n\n  const parsedRedirectUrl = new URL(redirectUrl)\n\n  if (host?.value !== parsedRedirectUrl.host) {\n    return null\n  }\n\n  // At this point the hosts are the same, just confirm we\n  // are routing to a path underneath the `basePath`\n  return parsedRedirectUrl.pathname.startsWith(basePath)\n    ? parsedRedirectUrl\n    : null\n}\n\nasync function createRedirectRenderResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  originalHost: Host,\n  redirectUrl: string,\n  redirectType: RedirectType,\n  basePath: string,\n  workStore: WorkStore\n) {\n  res.setHeader('x-action-redirect', `${redirectUrl};${redirectType}`)\n\n  // If we're redirecting to another route of this Next.js application, we'll\n  // try to stream the response from the other worker path. When that works,\n  // we can save an extra roundtrip and avoid a full page reload.\n  // When the redirect URL starts with a `/` or is to the same host, under the\n  // `basePath` we treat it as an app-relative redirect;\n  const appRelativeRedirectUrl = getAppRelativeRedirectUrl(\n    basePath,\n    originalHost,\n    redirectUrl\n  )\n\n  if (appRelativeRedirectUrl) {\n    if (!originalHost) {\n      throw new Error(\n        'Invariant: Missing `host` header from a forwarded Server Actions request.'\n      )\n    }\n\n    const forwardedHeaders = getForwardedHeaders(req, res)\n    forwardedHeaders.set(RSC_HEADER, '1')\n\n    const proto =\n      getRequestMeta(req, 'initProtocol')?.replace(/:+$/, '') || 'https'\n\n    // For standalone or the serverful mode, use the internal origin directly\n    // other than the host headers from the request.\n    const origin =\n      process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${originalHost.value}`\n\n    const fetchUrl = new URL(\n      `${origin}${appRelativeRedirectUrl.pathname}${appRelativeRedirectUrl.search}`\n    )\n\n    if (workStore.pendingRevalidatedTags) {\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n        workStore.pendingRevalidatedTags.join(',')\n      )\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n        workStore.incrementalCache?.prerenderManifest?.preview?.previewModeId ||\n          ''\n      )\n    }\n\n    // Ensures that when the path was revalidated we don't return a partial response on redirects\n    forwardedHeaders.delete(NEXT_ROUTER_STATE_TREE_HEADER)\n    // When an action follows a redirect, it's no longer handling an action: it's just a normal RSC request\n    // to the requested URL. We should remove the `next-action` header so that it's not treated as an action\n    forwardedHeaders.delete(ACTION_HEADER)\n\n    try {\n      setCacheBustingSearchParam(fetchUrl, {\n        [NEXT_ROUTER_PREFETCH_HEADER]: forwardedHeaders.get(\n          NEXT_ROUTER_PREFETCH_HEADER\n        )\n          ? ('1' as const)\n          : undefined,\n        [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]:\n          forwardedHeaders.get(NEXT_ROUTER_SEGMENT_PREFETCH_HEADER) ??\n          undefined,\n        [NEXT_ROUTER_STATE_TREE_HEADER]:\n          forwardedHeaders.get(NEXT_ROUTER_STATE_TREE_HEADER) ?? undefined,\n        [NEXT_URL]: forwardedHeaders.get(NEXT_URL) ?? undefined,\n      })\n\n      const response = await fetch(fetchUrl, {\n        method: 'GET',\n        headers: forwardedHeaders,\n        next: {\n          // @ts-ignore\n          internal: 1,\n        },\n      })\n\n      if (\n        response.headers\n          .get('content-type')\n          ?.startsWith(RSC_CONTENT_TYPE_HEADER)\n      ) {\n        // copy the headers from the redirect response to the response we're sending\n        for (const [key, value] of response.headers) {\n          if (!actionsForbiddenHeaders.includes(key)) {\n            res.setHeader(key, value)\n          }\n        }\n\n        return new FlightRenderResult(response.body!)\n      } else {\n        // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n        response.body?.cancel()\n      }\n    } catch (err) {\n      // we couldn't stream the redirect response, so we'll just do a normal redirect\n      console.error(`failed to get redirect response`, err)\n    }\n  }\n\n  return RenderResult.EMPTY\n}\n\n// Used to compare Host header and Origin header.\nconst enum HostType {\n  XForwardedHost = 'x-forwarded-host',\n  Host = 'host',\n}\ntype Host =\n  | {\n      type: HostType.XForwardedHost\n      value: string\n    }\n  | {\n      type: HostType.Host\n      value: string\n    }\n  | undefined\n\n/**\n * Ensures the value of the header can't create long logs.\n */\nfunction limitUntrustedHeaderValueForLogs(value: string) {\n  return value.length > 100 ? value.slice(0, 100) + '...' : value\n}\n\nexport function parseHostHeader(\n  headers: IncomingHttpHeaders,\n  originDomain?: string\n) {\n  const forwardedHostHeader = headers['x-forwarded-host']\n  const forwardedHostHeaderValue =\n    forwardedHostHeader && Array.isArray(forwardedHostHeader)\n      ? forwardedHostHeader[0]\n      : forwardedHostHeader?.split(',')?.[0]?.trim()\n  const hostHeader = headers['host']\n\n  if (originDomain) {\n    return forwardedHostHeaderValue === originDomain\n      ? {\n          type: HostType.XForwardedHost,\n          value: forwardedHostHeaderValue,\n        }\n      : hostHeader === originDomain\n        ? {\n            type: HostType.Host,\n            value: hostHeader,\n          }\n        : undefined\n  }\n\n  return forwardedHostHeaderValue\n    ? {\n        type: HostType.XForwardedHost,\n        value: forwardedHostHeaderValue,\n      }\n    : hostHeader\n      ? {\n          type: HostType.Host,\n          value: hostHeader,\n        }\n      : undefined\n}\n\ntype ServerModuleMap = Record<\n  string,\n  {\n    id: string\n    chunks: string[]\n    name: string\n  }\n>\n\ntype ServerActionsConfig = {\n  bodySizeLimit?: SizeLimit\n  allowedOrigins?: string[]\n}\n\ntype HandleActionResult =\n  | {\n      /** An MPA action threw notFound(), and we need to render the appropriate HTML */\n      type: 'not-found'\n    }\n  | {\n      type: 'done'\n      result: RenderResult | undefined\n      formState?: any\n    }\n  /** The request turned out not to be a server action. */\n  | null\n\nexport async function handleAction({\n  req,\n  res,\n  ComponentMod,\n  serverModuleMap,\n  generateFlight,\n  workStore,\n  requestStore,\n  serverActions,\n  ctx,\n  metadata,\n}: {\n  req: BaseNextRequest\n  res: BaseNextResponse\n  ComponentMod: AppPageModule\n  serverModuleMap: ServerModuleMap\n  generateFlight: GenerateFlight\n  workStore: WorkStore\n  requestStore: RequestStore\n  serverActions?: ServerActionsConfig\n  ctx: AppRenderContext\n  metadata: AppPageRenderResultMetadata\n}): Promise<HandleActionResult> {\n  const contentType = req.headers['content-type']\n  const { serverActionsManifest, page } = ctx.renderOpts\n\n  const {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isPossibleServerAction,\n  } = getServerActionRequestMetadata(req)\n\n  // If it can't be a Server Action, skip handling.\n  // Note that this can be a false positive -- any multipart/urlencoded POST can get us here,\n  // But won't know if it's an MPA action or not until we call `decodeAction` below.\n  if (!isPossibleServerAction) {\n    return null\n  }\n\n  if (workStore.isStaticGeneration) {\n    throw new Error(\n      \"Invariant: server actions can't be handled during static rendering\"\n    )\n  }\n\n  let temporaryReferences: TemporaryReferenceSet | undefined\n\n  // When running actions the default is no-store, you can still `cache: 'force-cache'`\n  workStore.fetchCache = 'default-no-store'\n\n  const originDomain =\n    typeof req.headers['origin'] === 'string'\n      ? new URL(req.headers['origin']).host\n      : undefined\n  const host = parseHostHeader(req.headers)\n\n  let warning: string | undefined = undefined\n\n  function warnBadServerActionRequest() {\n    if (warning) {\n      warn(warning)\n    }\n  }\n  // This is to prevent CSRF attacks. If `x-forwarded-host` is set, we need to\n  // ensure that the request is coming from the same host.\n  if (!originDomain) {\n    // This might be an old browser that doesn't send `host` header. We ignore\n    // this case.\n    warning = 'Missing `origin` header from a forwarded Server Actions request.'\n  } else if (!host || originDomain !== host.value) {\n    // If the customer sets a list of allowed origins, we'll allow the request.\n    // These are considered safe but might be different from forwarded host set\n    // by the infra (i.e. reverse proxies).\n    if (isCsrfOriginAllowed(originDomain, serverActions?.allowedOrigins)) {\n      // Ignore it\n    } else {\n      if (host) {\n        // This seems to be an CSRF attack. We should not proceed the action.\n        console.error(\n          `\\`${\n            host.type\n          }\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            host.value\n          )}\\` does not match \\`origin\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            originDomain\n          )}\\` from a forwarded Server Actions request. Aborting the action.`\n        )\n      } else {\n        // This is an attack. We should not proceed the action.\n        console.error(\n          `\\`x-forwarded-host\\` or \\`host\\` headers are not provided. One of these is needed to compare the \\`origin\\` header from a forwarded Server Actions request. Aborting the action.`\n        )\n      }\n\n      const error = new Error('Invalid Server Actions request.')\n\n      if (isFetchAction) {\n        res.statusCode = 500\n        metadata.statusCode = 500\n\n        const promise = Promise.reject(error)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n\n        return {\n          type: 'done',\n          result: await generateFlight(req, ctx, requestStore, {\n            actionResult: promise,\n            // We didn't execute an action, so no revalidations could have occurred. We can skip rendering the page.\n            skipFlight: true,\n            temporaryReferences,\n          }),\n        }\n      }\n\n      throw error\n    }\n  }\n\n  // ensure we avoid caching server actions unexpectedly\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  const { actionAsyncStorage } = ComponentMod\n\n  const actionWasForwarded = Boolean(req.headers['x-action-forwarded'])\n\n  if (actionId) {\n    const forwardedWorker = selectWorkerForForwarding(\n      actionId,\n      page,\n      serverActionsManifest\n    )\n\n    // If forwardedWorker is truthy, it means there isn't a worker for the action\n    // in the current handler, so we forward the request to a worker that has the action.\n    if (forwardedWorker) {\n      return {\n        type: 'done',\n        result: await createForwardedActionResponse(\n          req,\n          res,\n          host,\n          forwardedWorker,\n          ctx.renderOpts.basePath\n        ),\n      }\n    }\n  }\n\n  const handleUnrecognizedFetchAction = (err: unknown): HandleActionResult => {\n    // If the deployment doesn't have skew protection, this is expected to occasionally happen,\n    // so we use a warning instead of an error.\n    console.warn(err)\n\n    // Return an empty response with a header that the client router will interpret.\n    // We don't need to waste time encoding a flight response, and using a blank body + header\n    // means that unrecognized actions can also be handled at the infra level\n    // (i.e. without needing to invoke a lambda)\n    res.setHeader(NEXT_ACTION_NOT_FOUND_HEADER, '1')\n    res.setHeader('content-type', 'text/plain')\n    res.statusCode = 404\n    return {\n      type: 'done',\n      result: RenderResult.fromStatic('Server action not found.', 'text/plain'),\n    }\n  }\n\n  try {\n    return await actionAsyncStorage.run(\n      { isAction: true },\n      async (): Promise<HandleActionResult> => {\n        // We only use these for fetch actions -- MPA actions handle them inside `decodeAction`.\n        let actionModId: string | undefined\n        let boundActionArguments: unknown[] = []\n\n        if (\n          // The type check here ensures that `req` is correctly typed, and the\n          // environment variable check provides dead code elimination.\n          process.env.NEXT_RUNTIME === 'edge' &&\n          isWebNextRequest(req)\n        ) {\n          if (!req.body) {\n            throw new Error('invariant: Missing request body.')\n          }\n\n          // TODO: add body limit\n\n          // Use react-server-dom-webpack/server\n          const {\n            createTemporaryReferenceSet,\n            decodeReply,\n            decodeAction,\n            decodeFormState,\n          } = ComponentMod\n\n          temporaryReferences = createTemporaryReferenceSet()\n\n          if (isMultipartAction) {\n            // TODO-APP: Add streaming support\n            const formData = await req.request.formData()\n            if (isFetchAction) {\n              // A fetch action with a multipart body.\n              boundActionArguments = await decodeReply(\n                formData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              // Multipart POST, but not a fetch action.\n              // Potentially an MPA action, we have to try decoding it to check.\n              const action = await decodeAction(formData, serverModuleMap)\n              if (typeof action === 'function') {\n                // an MPA action.\n\n                // Only warn if it's a server action, otherwise skip for other post requests\n                warnBadServerActionRequest()\n\n                const actionReturnedState =\n                  await executeActionAndPrepareForRender(\n                    action as () => Promise<unknown>,\n                    [],\n                    workStore,\n                    requestStore\n                  )\n\n                const formState = await decodeFormState(\n                  actionReturnedState,\n                  formData,\n                  serverModuleMap\n                )\n\n                // Skip the fetch path.\n                // We need to render a full HTML version of the page for the response, we'll handle that in app-render.\n                return {\n                  type: 'done',\n                  result: undefined,\n                  formState,\n                }\n              } else {\n                // We couldn't decode an action, so this POST request turned out not to be a server action request.\n                return null\n              }\n            }\n          } else {\n            // POST with non-multipart body.\n\n            // If it's not multipart AND not a fetch action,\n            // then it can't be an action request.\n            if (!isFetchAction) {\n              return null\n            }\n\n            try {\n              actionModId = getActionModIdOrError(actionId, serverModuleMap)\n            } catch (err) {\n              return handleUnrecognizedFetchAction(err)\n            }\n\n            // A fetch action with a non-multipart body.\n            // In practice, this happens if `encodeReply` returned a string instead of FormData,\n            // which can happen for very simple JSON-like values that don't need multiple flight rows.\n\n            const chunks: Buffer[] = []\n            const reader = req.body.getReader()\n            while (true) {\n              const { done, value } = await reader.read()\n              if (done) {\n                break\n              }\n\n              chunks.push(value)\n            }\n\n            const actionData = Buffer.concat(chunks).toString('utf-8')\n\n            if (isURLEncodedAction) {\n              const formData = formDataFromSearchQueryString(actionData)\n              boundActionArguments = await decodeReply(\n                formData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              boundActionArguments = await decodeReply(\n                actionData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            }\n          }\n        } else if (\n          // The type check here ensures that `req` is correctly typed, and the\n          // environment variable check provides dead code elimination.\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          isNodeNextRequest(req)\n        ) {\n          // Use react-server-dom-webpack/server.node which supports streaming\n          const {\n            createTemporaryReferenceSet,\n            decodeReply,\n            decodeReplyFromBusboy,\n            decodeAction,\n            decodeFormState,\n          } = require(\n            `./react-server.node`\n          ) as typeof import('./react-server.node')\n\n          temporaryReferences = createTemporaryReferenceSet()\n\n          const { Transform, pipeline } =\n            require('node:stream') as typeof import('node:stream')\n\n          const defaultBodySizeLimit = '1 MB'\n          const bodySizeLimit =\n            serverActions?.bodySizeLimit ?? defaultBodySizeLimit\n          const bodySizeLimitBytes =\n            bodySizeLimit !== defaultBodySizeLimit\n              ? (\n                  require('next/dist/compiled/bytes') as typeof import('next/dist/compiled/bytes')\n                ).parse(bodySizeLimit)\n              : 1024 * 1024 // 1 MB\n\n          let size = 0\n          const sizeLimitTransform = new Transform({\n            transform(chunk, encoding, callback) {\n              size += Buffer.byteLength(chunk, encoding)\n              if (size > bodySizeLimitBytes) {\n                const { ApiError } =\n                  require('../api-utils') as typeof import('../api-utils')\n\n                callback(\n                  new ApiError(\n                    413,\n                    `Body exceeded ${bodySizeLimit} limit.\\n` +\n                      `To configure the body size limit for Server Actions, see: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit`\n                  )\n                )\n                return\n              }\n\n              callback(null, chunk)\n            },\n          })\n\n          const sizeLimitedBody = pipeline(\n            req.body,\n            sizeLimitTransform,\n            // Avoid unhandled errors from `pipeline()` by passing an empty completion callback.\n            // We'll propagate the errors properly when consuming the stream.\n            () => {}\n          )\n\n          if (isMultipartAction) {\n            if (isFetchAction) {\n              // A fetch action with a multipart body.\n\n              const busboy = (\n                require('next/dist/compiled/busboy') as typeof import('next/dist/compiled/busboy')\n              )({\n                defParamCharset: 'utf8',\n                headers: req.headers,\n                limits: { fieldSize: bodySizeLimitBytes },\n              })\n\n              // We need to use `pipeline(one, two)` instead of `one.pipe(two)` to propagate size limit errors correctly.\n              pipeline(\n                sizeLimitedBody,\n                busboy,\n                // Avoid unhandled errors from `pipeline()` by passing an empty completion callback.\n                // We'll propagate the errors properly when consuming the stream.\n                () => {}\n              )\n\n              boundActionArguments = await decodeReplyFromBusboy(\n                busboy,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              // Multipart POST, but not a fetch action.\n              // Potentially an MPA action, we have to try decoding it to check.\n\n              // React doesn't yet publish a busboy version of decodeAction\n              // so we polyfill the parsing of FormData.\n              const fakeRequest = new Request('http://localhost', {\n                method: 'POST',\n                // @ts-expect-error\n                headers: { 'Content-Type': contentType },\n                body: new ReadableStream({\n                  start: (controller) => {\n                    sizeLimitedBody.on('data', (chunk) => {\n                      controller.enqueue(new Uint8Array(chunk))\n                    })\n                    sizeLimitedBody.on('end', () => {\n                      controller.close()\n                    })\n                    sizeLimitedBody.on('error', (err) => {\n                      controller.error(err)\n                    })\n                  },\n                }),\n                duplex: 'half',\n              })\n              const formData = await fakeRequest.formData()\n              const action = await decodeAction(formData, serverModuleMap)\n              if (typeof action === 'function') {\n                // an MPA action.\n\n                // Only warn if it's a server action, otherwise skip for other post requests\n                warnBadServerActionRequest()\n\n                const actionReturnedState =\n                  await executeActionAndPrepareForRender(\n                    action as () => Promise<unknown>,\n                    [],\n                    workStore,\n                    requestStore\n                  )\n\n                const formState = await decodeFormState(\n                  actionReturnedState,\n                  formData,\n                  serverModuleMap\n                )\n\n                // Skip the fetch path.\n                // We need to render a full HTML version of the page for the response, we'll handle that in app-render.\n                return {\n                  type: 'done',\n                  result: undefined,\n                  formState,\n                }\n              } else {\n                // We couldn't decode an action, so this POST request turned out not to be a server action request.\n                return null\n              }\n            }\n          } else {\n            // POST with non-multipart body.\n\n            // If it's not multipart AND not a fetch action,\n            // then it can't be an action request.\n            if (!isFetchAction) {\n              return null\n            }\n\n            try {\n              actionModId = getActionModIdOrError(actionId, serverModuleMap)\n            } catch (err) {\n              return handleUnrecognizedFetchAction(err)\n            }\n\n            // A fetch action with a non-multipart body.\n            // In practice, this happens if `encodeReply` returned a string instead of FormData,\n            // which can happen for very simple JSON-like values that don't need multiple flight rows.\n\n            const chunks: Buffer[] = []\n            for await (const chunk of sizeLimitedBody) {\n              chunks.push(Buffer.from(chunk))\n            }\n\n            const actionData = Buffer.concat(chunks).toString('utf-8')\n\n            if (isURLEncodedAction) {\n              const formData = formDataFromSearchQueryString(actionData)\n              boundActionArguments = await decodeReply(\n                formData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              boundActionArguments = await decodeReply(\n                actionData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            }\n          }\n        } else {\n          throw new Error('Invariant: Unknown request type.')\n        }\n\n        // actions.js\n        // app/page.js\n        //   action worker1\n        //     appRender1\n\n        // app/foo/page.js\n        //   action worker2\n        //     appRender\n\n        // / -> fire action -> POST / -> appRender1 -> modId for the action file\n        // /foo -> fire action -> POST /foo -> appRender2 -> modId for the action file\n\n        try {\n          actionModId =\n            actionModId ?? getActionModIdOrError(actionId, serverModuleMap)\n        } catch (err) {\n          return handleUnrecognizedFetchAction(err)\n        }\n\n        const actionMod = (await ComponentMod.__next_app__.require(\n          actionModId\n        )) as Record<string, (...args: unknown[]) => Promise<unknown>>\n        const actionHandler =\n          actionMod[\n            // `actionId` must exist if we got here, as otherwise we would have thrown an error above\n            actionId!\n          ]\n\n        const returnVal = await executeActionAndPrepareForRender(\n          actionHandler,\n          boundActionArguments,\n          workStore,\n          requestStore\n        ).finally(() => {\n          addRevalidationHeader(res, { workStore, requestStore })\n        })\n\n        // For form actions, we need to continue rendering the page.\n        if (isFetchAction) {\n          const actionResult = await generateFlight(req, ctx, requestStore, {\n            actionResult: Promise.resolve(returnVal),\n            // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n            skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n            temporaryReferences,\n          })\n\n          return {\n            type: 'done',\n            result: actionResult,\n          }\n        } else {\n          // TODO: this shouldn't be reachable, because all non-fetch codepaths return early.\n          // this will be handled in a follow-up refactor PR.\n          return null\n        }\n      }\n    )\n  } catch (err) {\n    if (isRedirectError(err)) {\n      const redirectUrl = getURLFromRedirectError(err)\n      const redirectType = getRedirectTypeFromError(err)\n\n      // if it's a fetch action, we'll set the status code for logging/debugging purposes\n      // but we won't set a Location header, as the redirect will be handled by the client router\n      res.statusCode = RedirectStatusCode.SeeOther\n      metadata.statusCode = RedirectStatusCode.SeeOther\n\n      if (isFetchAction) {\n        return {\n          type: 'done',\n          result: await createRedirectRenderResult(\n            req,\n            res,\n            host,\n            redirectUrl,\n            redirectType,\n            ctx.renderOpts.basePath,\n            workStore\n          ),\n        }\n      }\n\n      // For an MPA action, the redirect doesn't need a body, just a Location header.\n      res.setHeader('Location', redirectUrl)\n      return {\n        type: 'done',\n        result: RenderResult.EMPTY,\n      }\n    } else if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n\n      if (isFetchAction) {\n        const promise = Promise.reject(err)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n        return {\n          type: 'done',\n          result: await generateFlight(req, ctx, requestStore, {\n            skipFlight: false,\n            actionResult: promise,\n            temporaryReferences,\n          }),\n        }\n      }\n\n      // For an MPA action, we need to render a HTML response. We'll handle that in app-render.\n      return {\n        type: 'not-found',\n      }\n    }\n\n    // An error that didn't come from `redirect()` or `notFound()`, likely thrown from user code\n    // (but it could also be a bug in our code!)\n\n    if (isFetchAction) {\n      // TODO: consider checking if the error is an `ApiError` and change status code\n      // so that we can respond with a 413 to requests that break the body size limit\n      // (but if we do that, we also need to make sure that whatever handles the non-fetch error path below does the same)\n      res.statusCode = 500\n      metadata.statusCode = 500\n      const promise = Promise.reject(err)\n      try {\n        // we need to await the promise to trigger the rejection early\n        // so that it's already handled by the time we call\n        // the RSC runtime. Otherwise, it will throw an unhandled\n        // promise rejection error in the renderer.\n        await promise\n      } catch {\n        // swallow error, it's gonna be handled on the client\n      }\n\n      return {\n        type: 'done',\n        result: await generateFlight(req, ctx, requestStore, {\n          actionResult: promise,\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        }),\n      }\n    }\n\n    // For an MPA action, we need to render a HTML response. We'll rethrow the error and let it be handled above.\n    throw err\n  }\n}\n\nasync function executeActionAndPrepareForRender<\n  TFn extends (...args: any[]) => Promise<any>,\n>(\n  action: TFn,\n  args: Parameters<TFn>,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Awaited<ReturnType<TFn>>> {\n  requestStore.phase = 'action'\n  try {\n    return await workUnitAsyncStorage.run(requestStore, () =>\n      action.apply(null, args)\n    )\n  } finally {\n    requestStore.phase = 'render'\n\n    // When we switch to the render phase, cookies() will return\n    // `workUnitStore.cookies` instead of `workUnitStore.userspaceMutableCookies`.\n    // We want the render to see any cookie writes that we performed during the action,\n    // so we need to update the immutable cookies to reflect the changes.\n    synchronizeMutableCookies(requestStore)\n\n    // The server action might have toggled draft mode, so we need to reflect\n    // that in the work store to be up-to-date for subsequent rendering.\n    workStore.isDraftMode = requestStore.draftMode.isEnabled\n\n    // If the action called revalidateTag/revalidatePath, then that might affect data used by the subsequent render,\n    // so we need to make sure all revalidations are applied before that\n    await executeRevalidates(workStore)\n  }\n}\n\n/**\n * Attempts to find the module ID for the action from the module map. When this fails, it could be a deployment skew where\n * the action came from a different deployment. It could also simply be an invalid POST request that is not a server action.\n * In either case, we'll throw an error to be handled by the caller.\n */\nfunction getActionModIdOrError(\n  actionId: string | null,\n  serverModuleMap: ServerModuleMap\n): string {\n  // if we're missing the action ID header, we can't do any further processing\n  if (!actionId) {\n    throw new InvariantError(\"Missing 'next-action' header.\")\n  }\n\n  const actionModId = serverModuleMap[actionId]?.id\n\n  if (!actionModId) {\n    throw new Error(\n      `Failed to find Server Action \"${actionId}\". This request might be from an older or newer deployment.\\nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action`\n    )\n  }\n\n  return actionModId\n}\n"], "names": ["RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "ACTION_HEADER", "NEXT_ACTION_NOT_FOUND_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_URL", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RenderResult", "FlightRenderResult", "filterReqHeaders", "actionsForbiddenHeaders", "getModifiedCookieValues", "JSON_CONTENT_TYPE_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "getServerActionRequestMetadata", "isCsrfOriginAllowed", "warn", "RequestCookies", "ResponseCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fromNodeOutgoingHttpHeaders", "selectWorkerForForwarding", "isNodeNextRequest", "isWebNextRequest", "RedirectStatusCode", "synchronizeMutableCookies", "workUnitAsyncStorage", "InvariantError", "executeRevalidates", "getRequestMeta", "setCacheBustingSearchParam", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "from", "responseHeaders", "getHeaders", "responseCookies", "mergedHeaders", "getAll", "for<PERSON>ach", "cookie", "delete", "name", "set", "toString", "Headers", "addRevalidationHeader", "workStore", "requestStore", "isTagRevalidated", "pendingRevalidatedTags", "length", "isCookieRevalidated", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createForwardedActionResponse", "host", "workerPathname", "basePath", "Error", "forwardedHeaders", "proto", "replace", "origin", "process", "env", "__NEXT_PRIVATE_ORIGIN", "fetchUrl", "URL", "response", "body", "NEXT_RUNTIME", "stream", "fetch", "method", "duplex", "redirect", "next", "internal", "get", "startsWith", "includes", "cancel", "err", "console", "error", "fromStatic", "getAppRelativeRedirectUrl", "redirectUrl", "parsedRedirectUrl", "pathname", "createRedirectRenderResult", "originalHost", "redirectType", "appRelativeRedirectUrl", "search", "incrementalCache", "prerenderManifest", "preview", "previewModeId", "EMPTY", "limitUntrustedHeaderValueForLogs", "slice", "parseHostHeader", "originDomain", "forwarded<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedHostHeaderValue", "split", "trim", "<PERSON><PERSON><PERSON><PERSON>", "type", "handleAction", "ComponentMod", "serverModuleMap", "generateFlight", "serverActions", "ctx", "metadata", "contentType", "serverActionsManifest", "page", "renderOpts", "actionId", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "isPossibleServerAction", "isStaticGeneration", "temporaryReferences", "fetchCache", "warning", "warnBadServerActionRequest", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "promise", "Promise", "reject", "result", "actionResult", "skipFlight", "actionAsyncStorage", "actionWasForwarded", "Boolean", "forwarded<PERSON><PERSON><PERSON>", "handleUnrecognizedFetchAction", "run", "isAction", "actionModId", "boundActionArguments", "createTemporaryReferenceSet", "decodeReply", "decodeAction", "decodeFormState", "request", "action", "actionReturnedState", "executeActionAndPrepareForRender", "formState", "getActionModIdOrError", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "push", "actionData", "<PERSON><PERSON><PERSON>", "concat", "decodeReplyFromBusboy", "require", "Transform", "pipeline", "defaultBodySizeLimit", "bodySizeLimit", "bodySizeLimitBytes", "parse", "size", "sizeLimitTransform", "transform", "chunk", "encoding", "callback", "byteLength", "ApiError", "sizeLimitedBody", "busboy", "def<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "limits", "fieldSize", "fakeRequest", "Request", "ReadableStream", "start", "controller", "on", "enqueue", "Uint8Array", "close", "actionMod", "__next_app__", "actionHandler", "returnVal", "finally", "resolve", "pathWasRevalidated", "<PERSON><PERSON><PERSON>", "args", "phase", "apply", "isDraftMode", "draftMode", "isEnabled", "id"], "mappings": "AAOA,SACEA,UAAU,EACVC,uBAAuB,EACvBC,6BAA6B,EAC7BC,aAAa,EACbC,4BAA4B,EAC5BC,2BAA2B,EAC3BC,mCAAmC,EACnCC,QAAQ,QACH,6CAA4C;AACnD,SACEC,2BAA2B,EAC3BC,yBAAyB,QACpB,oEAAmE;AAC1E,SACEC,wBAAwB,EACxBC,uBAAuB,QAClB,mCAAkC;AACzC,SACEC,eAAe,QAEV,yCAAwC;AAC/C,OAAOC,kBAEA,mBAAkB;AAEzB,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,gBAAgB,EAChBC,uBAAuB,QAClB,0BAAyB;AAChC,SAASC,uBAAuB,QAAQ,iDAAgD;AAExF,SACEC,wBAAwB,EACxBC,kCAAkC,EAClCC,sCAAsC,QACjC,sBAAqB;AAC5B,SAASC,8BAA8B,QAAQ,oCAAmC;AAClF,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,SAASC,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,cAAc,EAAEC,eAAe,QAAQ,gCAA+B;AAC/E,SAASC,cAAc,QAAQ,yCAAwC;AACvE,SAASC,2BAA2B,QAAQ,eAAc;AAC1D,SAASC,yBAAyB,QAAQ,iBAAgB;AAC1D,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,uBAAsB;AAC1E,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,yBAAyB,QAAQ,iCAAgC;AAE1E,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,kBAAiB;AAChD,SAASC,0BAA0B,QAAQ,wEAAuE;AAElH,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,GAAGV,OAAO;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAqB;IAErB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiB,IAAIpC,eAAeE,eAAemC,IAAI,CAACF;IAE9D,mCAAmC;IACnC,MAAMG,kBAAkBJ,IAAIK,UAAU;IACtC,MAAMC,kBAAkB,IAAIvC,gBAC1BE,4BAA4BmC;IAG9B,qCAAqC;IACrC,MAAMG,gBAAgBlD,iBACpB;QACE,GAAGgC,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBe,gBAAgB;IACzC,GACA9C;IAGF,+EAA+E;IAC/E,kDAAkD;IAClDgD,gBAAgBE,MAAM,GAAGC,OAAO,CAAC,CAACC;QAChC,IAAI,OAAOA,OAAOvB,KAAK,KAAK,aAAa;YACvCe,eAAeS,MAAM,CAACD,OAAOE,IAAI;QACnC,OAAO;YACLV,eAAeW,GAAG,CAACH;QACrB;IACF;IAEA,qDAAqD;IACrDH,aAAa,CAAC,SAAS,GAAGL,eAAeY,QAAQ;IAEjD,8CAA8C;IAC9C,OAAOP,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIQ,QAAQR;AACrB;AAEA,SAASS,sBACPhB,GAAqB,EACrB,EACEiB,SAAS,EACTC,YAAY,EAIb;QAewBD;IAbzB,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAME,mBAAmBF,EAAAA,oCAAAA,UAAUG,sBAAsB,qBAAhCH,kCAAkCI,MAAM,IAAG,IAAI;IACxE,MAAMC,sBAAsB/D,wBAC1B2D,aAAaK,cAAc,EAC3BF,MAAM,GACJ,IACA;IAEJrB,IAAIwB,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAEP;QAAkBG;KAAoB;AAE9D;AAEA;;CAEC,GACD,eAAeK,8BACb5B,GAAoB,EACpBC,GAAqB,EACrB4B,IAAU,EACVC,cAAsB,EACtBC,QAAgB;QAgBdpD;IAdF,IAAI,CAACkD,MAAM;QACT,MAAM,qBAEL,CAFK,IAAIG,MACR,8EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,mBAAmBlC,oBAAoBC,KAAKC;IAElD,sEAAsE;IACtE,+EAA+E;IAC/E,8CAA8C;IAC9CgC,iBAAiBnB,GAAG,CAAC,sBAAsB;IAE3C,MAAMoB,QACJvD,EAAAA,kBAAAA,eAAeqB,KAAK,oCAApBrB,gBAAqCwD,OAAO,CAAC,OAAO,QAAO;IAE7D,yEAAyE;IACzE,gDAAgD;IAChD,MAAMC,SAASC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGL,MAAM,GAAG,EAAEL,KAAKzC,KAAK,EAAE;IAE9E,MAAMoD,WAAW,IAAIC,IAAI,GAAGL,SAASL,WAAWD,gBAAgB;IAEhE,IAAI;YAsCAY;QArCF,IAAIC;QACJ,IACE,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BvE,iBAAiB2B,MACjB;YACA,IAAI,CAACA,IAAI2C,IAAI,EAAE;gBACb,MAAM,qBAA6C,CAA7C,IAAIX,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEAW,OAAO3C,IAAI2C,IAAI;QACjB,OAAO,IACL,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BxE,kBAAkB4B,MAClB;YACA2C,OAAO3C,IAAI6C,MAAM;QACnB,OAAO;YACL,MAAM,qBAA6C,CAA7C,IAAIb,MAAM,qCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA4C;QACpD;QAEA,wCAAwC;QACxC,MAAMU,WAAW,MAAMI,MAAMN,UAAU;YACrCO,QAAQ;YACRJ;YACAK,QAAQ;YACRzD,SAAS0C;YACTgB,UAAU;YACVC,MAAM;gBACJ,aAAa;gBACbC,UAAU;YACZ;QACF;QAEA,KACET,wBAAAA,SAASnD,OAAO,CAAC6D,GAAG,CAAC,oCAArBV,sBAAsCW,UAAU,CAAC7G,0BACjD;YACA,4EAA4E;YAC5E,KAAK,MAAM,CAAC2C,KAAKC,MAAM,IAAIsD,SAASnD,OAAO,CAAE;gBAC3C,IAAI,CAAChC,wBAAwB+F,QAAQ,CAACnE,MAAM;oBAC1Cc,IAAIwB,SAAS,CAACtC,KAAKC;gBACrB;YACF;YAEA,OAAO,IAAI/B,mBAAmBqF,SAASC,IAAI;QAC7C,OAAO;gBACL,kFAAkF;YAClFD;aAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAea,MAAM;QACvB;IACF,EAAE,OAAOC,KAAK;QACZ,oFAAoF;QACpFC,QAAQC,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAEF;IACrD;IAEA,OAAOpG,aAAauG,UAAU,CAAC,MAAMlG;AACvC;AAEA;;;;;;CAMC,GACD,SAASmG,0BACP7B,QAAgB,EAChBF,IAAU,EACVgC,WAAmB;IAEnB,IAAIA,YAAYR,UAAU,CAAC,QAAQQ,YAAYR,UAAU,CAAC,MAAM;QAC9D,2DAA2D;QAC3D,OAAO,IAAIZ,IAAI,GAAGV,WAAW8B,aAAa,EAAE;IAC9C;IAEA,MAAMC,oBAAoB,IAAIrB,IAAIoB;IAElC,IAAIhC,CAAAA,wBAAAA,KAAMzC,KAAK,MAAK0E,kBAAkBjC,IAAI,EAAE;QAC1C,OAAO;IACT;IAEA,wDAAwD;IACxD,kDAAkD;IAClD,OAAOiC,kBAAkBC,QAAQ,CAACV,UAAU,CAACtB,YACzC+B,oBACA;AACN;AAEA,eAAeE,2BACbhE,GAAoB,EACpBC,GAAqB,EACrBgE,YAAkB,EAClBJ,WAAmB,EACnBK,YAA0B,EAC1BnC,QAAgB,EAChBb,SAAoB;IAEpBjB,IAAIwB,SAAS,CAAC,qBAAqB,GAAGoC,YAAY,CAAC,EAAEK,cAAc;IAEnE,2EAA2E;IAC3E,0EAA0E;IAC1E,+DAA+D;IAC/D,4EAA4E;IAC5E,sDAAsD;IACtD,MAAMC,yBAAyBP,0BAC7B7B,UACAkC,cACAJ;IAGF,IAAIM,wBAAwB;YAWxBxF;QAVF,IAAI,CAACsF,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAIjC,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMC,mBAAmBlC,oBAAoBC,KAAKC;QAClDgC,iBAAiBnB,GAAG,CAACvE,YAAY;QAEjC,MAAM2F,QACJvD,EAAAA,kBAAAA,eAAeqB,KAAK,oCAApBrB,gBAAqCwD,OAAO,CAAC,OAAO,QAAO;QAE7D,yEAAyE;QACzE,gDAAgD;QAChD,MAAMC,SACJC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGL,MAAM,GAAG,EAAE+B,aAAa7E,KAAK,EAAE;QAEzE,MAAMoD,WAAW,IAAIC,IACnB,GAAGL,SAAS+B,uBAAuBJ,QAAQ,GAAGI,uBAAuBC,MAAM,EAAE;QAG/E,IAAIlD,UAAUG,sBAAsB,EAAE;gBAOlCH,uDAAAA,+CAAAA;YANFe,iBAAiBnB,GAAG,CAClBpD,oCACAwD,UAAUG,sBAAsB,CAACvB,IAAI,CAAC;YAExCmC,iBAAiBnB,GAAG,CAClBnD,wCACAuD,EAAAA,8BAAAA,UAAUmD,gBAAgB,sBAA1BnD,gDAAAA,4BAA4BoD,iBAAiB,sBAA7CpD,wDAAAA,8CAA+CqD,OAAO,qBAAtDrD,sDAAwDsD,aAAa,KACnE;QAEN;QAEA,6FAA6F;QAC7FvC,iBAAiBrB,MAAM,CAACnE;QACxB,uGAAuG;QACvG,wGAAwG;QACxGwF,iBAAiBrB,MAAM,CAAClE;QAExB,IAAI;gBAyBAgG;YAxBF9D,2BAA2B4D,UAAU;gBACnC,CAAC5F,4BAA4B,EAAEqF,iBAAiBmB,GAAG,CACjDxG,+BAEG,MACD+C;gBACJ,CAAC9C,oCAAoC,EACnCoF,iBAAiBmB,GAAG,CAACvG,wCACrB8C;gBACF,CAAClD,8BAA8B,EAC7BwF,iBAAiBmB,GAAG,CAAC3G,kCAAkCkD;gBACzD,CAAC7C,SAAS,EAAEmF,iBAAiBmB,GAAG,CAACtG,aAAa6C;YAChD;YAEA,MAAM+C,WAAW,MAAMI,MAAMN,UAAU;gBACrCO,QAAQ;gBACRxD,SAAS0C;gBACTiB,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,KACET,wBAAAA,SAASnD,OAAO,CACb6D,GAAG,CAAC,oCADPV,sBAEIW,UAAU,CAAC7G,0BACf;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAAC2C,KAAKC,MAAM,IAAIsD,SAASnD,OAAO,CAAE;oBAC3C,IAAI,CAAChC,wBAAwB+F,QAAQ,CAACnE,MAAM;wBAC1Cc,IAAIwB,SAAS,CAACtC,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAI/B,mBAAmBqF,SAASC,IAAI;YAC7C,OAAO;oBACL,kFAAkF;gBAClFD;iBAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAea,MAAM;YACvB;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEF;QACnD;IACF;IAEA,OAAOpG,aAAaqH,KAAK;AAC3B;AAkBA;;CAEC,GACD,SAASC,iCAAiCtF,KAAa;IACrD,OAAOA,MAAMkC,MAAM,GAAG,MAAMlC,MAAMuF,KAAK,CAAC,GAAG,OAAO,QAAQvF;AAC5D;AAEA,OAAO,SAASwF,gBACdrF,OAA4B,EAC5BsF,YAAqB;QAMfC,6BAAAA;IAJN,MAAMA,sBAAsBvF,OAAO,CAAC,mBAAmB;IACvD,MAAMwF,2BACJD,uBAAuBlF,MAAMC,OAAO,CAACiF,uBACjCA,mBAAmB,CAAC,EAAE,GACtBA,wCAAAA,6BAAAA,oBAAqBE,KAAK,CAAC,0BAA3BF,8BAAAA,0BAAiC,CAAC,EAAE,qBAApCA,4BAAsCG,IAAI;IAChD,MAAMC,aAAa3F,OAAO,CAAC,OAAO;IAElC,IAAIsF,cAAc;QAChB,OAAOE,6BAA6BF,eAChC;YACEM,IAAI;YACJ/F,OAAO2F;QACT,IACAG,eAAeL,eACb;YACEM,IAAI;YACJ/F,OAAO8F;QACT,IACAvF;IACR;IAEA,OAAOoF,2BACH;QACEI,IAAI;QACJ/F,OAAO2F;IACT,IACAG,aACE;QACEC,IAAI;QACJ/F,OAAO8F;IACT,IACAvF;AACR;AA6BA,OAAO,eAAeyF,aAAa,EACjCpF,GAAG,EACHC,GAAG,EACHoF,YAAY,EACZC,eAAe,EACfC,cAAc,EACdrE,SAAS,EACTC,YAAY,EACZqE,aAAa,EACbC,GAAG,EACHC,QAAQ,EAYT;IACC,MAAMC,cAAc3F,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAM,EAAEqG,qBAAqB,EAAEC,IAAI,EAAE,GAAGJ,IAAIK,UAAU;IAEtD,MAAM,EACJC,QAAQ,EACRC,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EACbC,sBAAsB,EACvB,GAAGvI,+BAA+BoC;IAEnC,iDAAiD;IACjD,2FAA2F;IAC3F,kFAAkF;IAClF,IAAI,CAACmG,wBAAwB;QAC3B,OAAO;IACT;IAEA,IAAIjF,UAAUkF,kBAAkB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIpE,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIqE;IAEJ,qFAAqF;IACrFnF,UAAUoF,UAAU,GAAG;IAEvB,MAAMzB,eACJ,OAAO7E,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAIkD,IAAIzC,IAAIT,OAAO,CAAC,SAAS,EAAEsC,IAAI,GACnClC;IACN,MAAMkC,OAAO+C,gBAAgB5E,IAAIT,OAAO;IAExC,IAAIgH,UAA8B5G;IAElC,SAAS6G;QACP,IAAID,SAAS;YACXzI,KAAKyI;QACP;IACF;IACA,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAAC1B,cAAc;QACjB,0EAA0E;QAC1E,aAAa;QACb0B,UAAU;IACZ,OAAO,IAAI,CAAC1E,QAAQgD,iBAAiBhD,KAAKzC,KAAK,EAAE;QAC/C,2EAA2E;QAC3E,2EAA2E;QAC3E,uCAAuC;QACvC,IAAIvB,oBAAoBgH,cAAcW,iCAAAA,cAAeiB,cAAc,GAAG;QACpE,YAAY;QACd,OAAO;YACL,IAAI5E,MAAM;gBACR,qEAAqE;gBACrE4B,QAAQC,KAAK,CACX,CAAC,EAAE,EACD7B,KAAKsD,IAAI,CACV,uBAAuB,EAAET,iCACxB7C,KAAKzC,KAAK,EACV,iDAAiD,EAAEsF,iCACnDG,cACA,gEAAgE,CAAC;YAEvE,OAAO;gBACL,uDAAuD;gBACvDpB,QAAQC,KAAK,CACX,CAAC,gLAAgL,CAAC;YAEtL;YAEA,MAAMA,QAAQ,qBAA4C,CAA5C,IAAI1B,MAAM,oCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA2C;YAEzD,IAAIkE,eAAe;gBACjBjG,IAAIyG,UAAU,GAAG;gBACjBhB,SAASgB,UAAU,GAAG;gBAEtB,MAAMC,UAAUC,QAAQC,MAAM,CAACnD;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAMiD;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBAEA,OAAO;oBACLxB,MAAM;oBACN2B,QAAQ,MAAMvB,eAAevF,KAAKyF,KAAKtE,cAAc;wBACnD4F,cAAcJ;wBACd,wGAAwG;wBACxGK,YAAY;wBACZX;oBACF;gBACF;YACF;YAEA,MAAM3C;QACR;IACF;IAEA,sDAAsD;IACtDzD,IAAIwB,SAAS,CACX,iBACA;IAGF,MAAM,EAAEwF,kBAAkB,EAAE,GAAG5B;IAE/B,MAAM6B,qBAAqBC,QAAQnH,IAAIT,OAAO,CAAC,qBAAqB;IAEpE,IAAIwG,UAAU;QACZ,MAAMqB,kBAAkBjJ,0BACtB4H,UACAF,MACAD;QAGF,6EAA6E;QAC7E,qFAAqF;QACrF,IAAIwB,iBAAiB;YACnB,OAAO;gBACLjC,MAAM;gBACN2B,QAAQ,MAAMlF,8BACZ5B,KACAC,KACA4B,MACAuF,iBACA3B,IAAIK,UAAU,CAAC/D,QAAQ;YAE3B;QACF;IACF;IAEA,MAAMsF,gCAAgC,CAAC7D;QACrC,2FAA2F;QAC3F,2CAA2C;QAC3CC,QAAQ3F,IAAI,CAAC0F;QAEb,gFAAgF;QAChF,0FAA0F;QAC1F,yEAAyE;QACzE,4CAA4C;QAC5CvD,IAAIwB,SAAS,CAAC9E,8BAA8B;QAC5CsD,IAAIwB,SAAS,CAAC,gBAAgB;QAC9BxB,IAAIyG,UAAU,GAAG;QACjB,OAAO;YACLvB,MAAM;YACN2B,QAAQ1J,aAAauG,UAAU,CAAC,4BAA4B;QAC9D;IACF;IAEA,IAAI;QACF,OAAO,MAAMsD,mBAAmBK,GAAG,CACjC;YAAEC,UAAU;QAAK,GACjB;YACE,wFAAwF;YACxF,IAAIC;YACJ,IAAIC,uBAAkC,EAAE;YAExC,IACE,qEAAqE;YACrE,6DAA6D;YAC7DpF,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BvE,iBAAiB2B,MACjB;gBACA,IAAI,CAACA,IAAI2C,IAAI,EAAE;oBACb,MAAM,qBAA6C,CAA7C,IAAIX,MAAM,qCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4C;gBACpD;gBAEA,uBAAuB;gBAEvB,sCAAsC;gBACtC,MAAM,EACJ0F,2BAA2B,EAC3BC,WAAW,EACXC,YAAY,EACZC,eAAe,EAChB,GAAGxC;gBAEJgB,sBAAsBqB;gBAEtB,IAAIzB,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAMhH,WAAW,MAAMe,IAAI8H,OAAO,CAAC7I,QAAQ;oBAC3C,IAAIiH,eAAe;wBACjB,wCAAwC;wBACxCuB,uBAAuB,MAAME,YAC3B1I,UACAqG,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACL,0CAA0C;wBAC1C,kEAAkE;wBAClE,MAAM0B,SAAS,MAAMH,aAAa3I,UAAUqG;wBAC5C,IAAI,OAAOyC,WAAW,YAAY;4BAChC,iBAAiB;4BAEjB,4EAA4E;4BAC5EvB;4BAEA,MAAMwB,sBACJ,MAAMC,iCACJF,QACA,EAAE,EACF7G,WACAC;4BAGJ,MAAM+G,YAAY,MAAML,gBACtBG,qBACA/I,UACAqG;4BAGF,uBAAuB;4BACvB,uGAAuG;4BACvG,OAAO;gCACLH,MAAM;gCACN2B,QAAQnH;gCACRuI;4BACF;wBACF,OAAO;4BACL,mGAAmG;4BACnG,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,gCAAgC;oBAEhC,gDAAgD;oBAChD,sCAAsC;oBACtC,IAAI,CAAChC,eAAe;wBAClB,OAAO;oBACT;oBAEA,IAAI;wBACFsB,cAAcW,sBAAsBpC,UAAUT;oBAChD,EAAE,OAAO9B,KAAK;wBACZ,OAAO6D,8BAA8B7D;oBACvC;oBAEA,4CAA4C;oBAC5C,oFAAoF;oBACpF,0FAA0F;oBAE1F,MAAM4E,SAAmB,EAAE;oBAC3B,MAAMC,SAASrI,IAAI2C,IAAI,CAAC2F,SAAS;oBACjC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAEnJ,KAAK,EAAE,GAAG,MAAMiJ,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,OAAOK,IAAI,CAACrJ;oBACd;oBAEA,MAAMsJ,aAAaC,OAAOC,MAAM,CAACR,QAAQrH,QAAQ,CAAC;oBAElD,IAAIiF,oBAAoB;wBACtB,MAAM/G,WAAWJ,8BAA8B6J;wBAC/CjB,uBAAuB,MAAME,YAC3B1I,UACAqG,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACLoB,uBAAuB,MAAME,YAC3Be,YACApD,iBACA;4BAAEe;wBAAoB;oBAE1B;gBACF;YACF,OAAO,IACL,qEAAqE;YACrE,6DAA6D;YAC7DhE,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BxE,kBAAkB4B,MAClB;gBACA,oEAAoE;gBACpE,MAAM,EACJ0H,2BAA2B,EAC3BC,WAAW,EACXkB,qBAAqB,EACrBjB,YAAY,EACZC,eAAe,EAChB,GAAGiB,QACF,CAAC,mBAAmB,CAAC;gBAGvBzC,sBAAsBqB;gBAEtB,MAAM,EAAEqB,SAAS,EAAEC,QAAQ,EAAE,GAC3BF,QAAQ;gBAEV,MAAMG,uBAAuB;gBAC7B,MAAMC,gBACJ1D,CAAAA,iCAAAA,cAAe0D,aAAa,KAAID;gBAClC,MAAME,qBACJD,kBAAkBD,uBACd,AACEH,QAAQ,4BACRM,KAAK,CAACF,iBACR,OAAO,KAAK,OAAO;;gBAEzB,IAAIG,OAAO;gBACX,MAAMC,qBAAqB,IAAIP,UAAU;oBACvCQ,WAAUC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ;wBACjCL,QAAQV,OAAOgB,UAAU,CAACH,OAAOC;wBACjC,IAAIJ,OAAOF,oBAAoB;4BAC7B,MAAM,EAAES,QAAQ,EAAE,GAChBd,QAAQ;4BAEVY,SACE,qBAIC,CAJD,IAAIE,SACF,KACA,CAAC,cAAc,EAAEV,cAAc,SAAS,CAAC,GACvC,CAAC,8IAA8I,CAAC,GAHpJ,qBAAA;uCAAA;4CAAA;8CAAA;4BAIA;4BAEF;wBACF;wBAEAQ,SAAS,MAAMF;oBACjB;gBACF;gBAEA,MAAMK,kBAAkBb,SACtBhJ,IAAI2C,IAAI,EACR2G,oBACA,oFAAoF;gBACpF,iEAAiE;gBACjE,KAAO;gBAGT,IAAIrD,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,wCAAwC;wBAExC,MAAM4D,SAAS,AACbhB,QAAQ,6BACR;4BACAiB,iBAAiB;4BACjBxK,SAASS,IAAIT,OAAO;4BACpByK,QAAQ;gCAAEC,WAAWd;4BAAmB;wBAC1C;wBAEA,2GAA2G;wBAC3GH,SACEa,iBACAC,QACA,oFAAoF;wBACpF,iEAAiE;wBACjE,KAAO;wBAGTrC,uBAAuB,MAAMoB,sBAC3BiB,QACAxE,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACL,0CAA0C;wBAC1C,kEAAkE;wBAElE,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAM6D,cAAc,IAAIC,QAAQ,oBAAoB;4BAClDpH,QAAQ;4BACR,mBAAmB;4BACnBxD,SAAS;gCAAE,gBAAgBoG;4BAAY;4BACvChD,MAAM,IAAIyH,eAAe;gCACvBC,OAAO,CAACC;oCACNT,gBAAgBU,EAAE,CAAC,QAAQ,CAACf;wCAC1Bc,WAAWE,OAAO,CAAC,IAAIC,WAAWjB;oCACpC;oCACAK,gBAAgBU,EAAE,CAAC,OAAO;wCACxBD,WAAWI,KAAK;oCAClB;oCACAb,gBAAgBU,EAAE,CAAC,SAAS,CAAC/G;wCAC3B8G,WAAW5G,KAAK,CAACF;oCACnB;gCACF;4BACF;4BACAR,QAAQ;wBACV;wBACA,MAAM/D,WAAW,MAAMiL,YAAYjL,QAAQ;wBAC3C,MAAM8I,SAAS,MAAMH,aAAa3I,UAAUqG;wBAC5C,IAAI,OAAOyC,WAAW,YAAY;4BAChC,iBAAiB;4BAEjB,4EAA4E;4BAC5EvB;4BAEA,MAAMwB,sBACJ,MAAMC,iCACJF,QACA,EAAE,EACF7G,WACAC;4BAGJ,MAAM+G,YAAY,MAAML,gBACtBG,qBACA/I,UACAqG;4BAGF,uBAAuB;4BACvB,uGAAuG;4BACvG,OAAO;gCACLH,MAAM;gCACN2B,QAAQnH;gCACRuI;4BACF;wBACF,OAAO;4BACL,mGAAmG;4BACnG,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,gCAAgC;oBAEhC,gDAAgD;oBAChD,sCAAsC;oBACtC,IAAI,CAAChC,eAAe;wBAClB,OAAO;oBACT;oBAEA,IAAI;wBACFsB,cAAcW,sBAAsBpC,UAAUT;oBAChD,EAAE,OAAO9B,KAAK;wBACZ,OAAO6D,8BAA8B7D;oBACvC;oBAEA,4CAA4C;oBAC5C,oFAAoF;oBACpF,0FAA0F;oBAE1F,MAAM4E,SAAmB,EAAE;oBAC3B,WAAW,MAAMoB,SAASK,gBAAiB;wBACzCzB,OAAOK,IAAI,CAACE,OAAOvI,IAAI,CAACoJ;oBAC1B;oBAEA,MAAMd,aAAaC,OAAOC,MAAM,CAACR,QAAQrH,QAAQ,CAAC;oBAElD,IAAIiF,oBAAoB;wBACtB,MAAM/G,WAAWJ,8BAA8B6J;wBAC/CjB,uBAAuB,MAAME,YAC3B1I,UACAqG,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACLoB,uBAAuB,MAAME,YAC3Be,YACApD,iBACA;4BAAEe;wBAAoB;oBAE1B;gBACF;YACF,OAAO;gBACL,MAAM,qBAA6C,CAA7C,IAAIrE,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,IAAI;gBACFwF,cACEA,eAAeW,sBAAsBpC,UAAUT;YACnD,EAAE,OAAO9B,KAAK;gBACZ,OAAO6D,8BAA8B7D;YACvC;YAEA,MAAMmH,YAAa,MAAMtF,aAAauF,YAAY,CAAC9B,OAAO,CACxDtB;YAEF,MAAMqD,gBACJF,SAAS,CACP,yFAAyF;YACzF5E,SACD;YAEH,MAAM+E,YAAY,MAAM7C,iCACtB4C,eACApD,sBACAvG,WACAC,cACA4J,OAAO,CAAC;gBACR9J,sBAAsBhB,KAAK;oBAAEiB;oBAAWC;gBAAa;YACvD;YAEA,4DAA4D;YAC5D,IAAI+E,eAAe;gBACjB,MAAMa,eAAe,MAAMxB,eAAevF,KAAKyF,KAAKtE,cAAc;oBAChE4F,cAAcH,QAAQoE,OAAO,CAACF;oBAC9B,iIAAiI;oBACjI9D,YAAY,CAAC9F,UAAU+J,kBAAkB,IAAI/D;oBAC7Cb;gBACF;gBAEA,OAAO;oBACLlB,MAAM;oBACN2B,QAAQC;gBACV;YACF,OAAO;gBACL,mFAAmF;gBACnF,mDAAmD;gBACnD,OAAO;YACT;QACF;IAEJ,EAAE,OAAOvD,KAAK;QACZ,IAAIrG,gBAAgBqG,MAAM;YACxB,MAAMK,cAAc3G,wBAAwBsG;YAC5C,MAAMU,eAAejH,yBAAyBuG;YAE9C,mFAAmF;YACnF,2FAA2F;YAC3FvD,IAAIyG,UAAU,GAAGpI,mBAAmB4M,QAAQ;YAC5CxF,SAASgB,UAAU,GAAGpI,mBAAmB4M,QAAQ;YAEjD,IAAIhF,eAAe;gBACjB,OAAO;oBACLf,MAAM;oBACN2B,QAAQ,MAAM9C,2BACZhE,KACAC,KACA4B,MACAgC,aACAK,cACAuB,IAAIK,UAAU,CAAC/D,QAAQ,EACvBb;gBAEJ;YACF;YAEA,+EAA+E;YAC/EjB,IAAIwB,SAAS,CAAC,YAAYoC;YAC1B,OAAO;gBACLsB,MAAM;gBACN2B,QAAQ1J,aAAaqH,KAAK;YAC5B;QACF,OAAO,IAAIzH,0BAA0BwG,MAAM;YACzCvD,IAAIyG,UAAU,GAAG3J,4BAA4ByG;YAC7CkC,SAASgB,UAAU,GAAGzG,IAAIyG,UAAU;YAEpC,IAAIR,eAAe;gBACjB,MAAMS,UAAUC,QAAQC,MAAM,CAACrD;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAMmD;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBACA,OAAO;oBACLxB,MAAM;oBACN2B,QAAQ,MAAMvB,eAAevF,KAAKyF,KAAKtE,cAAc;wBACnD6F,YAAY;wBACZD,cAAcJ;wBACdN;oBACF;gBACF;YACF;YAEA,yFAAyF;YACzF,OAAO;gBACLlB,MAAM;YACR;QACF;QAEA,4FAA4F;QAC5F,4CAA4C;QAE5C,IAAIe,eAAe;YACjB,+EAA+E;YAC/E,+EAA+E;YAC/E,oHAAoH;YACpHjG,IAAIyG,UAAU,GAAG;YACjBhB,SAASgB,UAAU,GAAG;YACtB,MAAMC,UAAUC,QAAQC,MAAM,CAACrD;YAC/B,IAAI;gBACF,8DAA8D;gBAC9D,mDAAmD;gBACnD,yDAAyD;gBACzD,2CAA2C;gBAC3C,MAAMmD;YACR,EAAE,OAAM;YACN,qDAAqD;YACvD;YAEA,OAAO;gBACLxB,MAAM;gBACN2B,QAAQ,MAAMvB,eAAevF,KAAKyF,KAAKtE,cAAc;oBACnD4F,cAAcJ;oBACd,iIAAiI;oBACjIK,YAAY,CAAC9F,UAAU+J,kBAAkB,IAAI/D;oBAC7Cb;gBACF;YACF;QACF;QAEA,6GAA6G;QAC7G,MAAM7C;IACR;AACF;AAEA,eAAeyE,iCAGbF,MAAW,EACXoD,IAAqB,EACrBjK,SAAoB,EACpBC,YAA0B;IAE1BA,aAAaiK,KAAK,GAAG;IACrB,IAAI;QACF,OAAO,MAAM5M,qBAAqB8I,GAAG,CAACnG,cAAc,IAClD4G,OAAOsD,KAAK,CAAC,MAAMF;IAEvB,SAAU;QACRhK,aAAaiK,KAAK,GAAG;QAErB,4DAA4D;QAC5D,8EAA8E;QAC9E,mFAAmF;QACnF,qEAAqE;QACrE7M,0BAA0B4C;QAE1B,yEAAyE;QACzE,oEAAoE;QACpED,UAAUoK,WAAW,GAAGnK,aAAaoK,SAAS,CAACC,SAAS;QAExD,gHAAgH;QAChH,oEAAoE;QACpE,MAAM9M,mBAAmBwC;IAC3B;AACF;AAEA;;;;CAIC,GACD,SAASiH,sBACPpC,QAAuB,EACvBT,eAAgC;QAOZA;IALpB,4EAA4E;IAC5E,IAAI,CAACS,UAAU;QACb,MAAM,qBAAmD,CAAnD,IAAItH,eAAe,kCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAkD;IAC1D;IAEA,MAAM+I,eAAclC,4BAAAA,eAAe,CAACS,SAAS,qBAAzBT,0BAA2BmG,EAAE;IAEjD,IAAI,CAACjE,aAAa;QAChB,MAAM,qBAEL,CAFK,IAAIxF,MACR,CAAC,8BAA8B,EAAE+D,SAAS,qIAAqI,CAAC,GAD5K,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOyB;AACT", "ignoreList": [0]}