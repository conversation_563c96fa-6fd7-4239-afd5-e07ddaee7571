{"version": 3, "sources": ["../../../src/server/app-render/after-task-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { afterTaskAsyncStorageInstance as afterTaskAsyncStorage } from './after-task-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { WorkUnitStore } from './work-unit-async-storage.external'\n\nexport interface AfterTaskStore {\n  /** The phase in which the topmost `after` was called.\n   *\n   * NOTE: Can be undefined when running `generateStaticParams`,\n   * where we only have a `workStore`, no `workUnitStore`.\n   */\n  readonly rootTaskSpawnPhase: WorkUnitStore['phase'] | undefined\n}\n\nexport type AfterTaskAsyncStorage = AsyncLocalStorage<AfterTaskStore>\n\nexport { afterTaskAsyncStorage }\n"], "names": ["afterTaskAsyncStorageInstance", "afterTaskAsyncStorage"], "mappings": "AAEA,qDAAqD;AACrD,SAASA,iCAAiCC,qBAAqB,QAAQ,2CAA2C;IAAE,wBAAwB;AAAc,EAAC;AAc3J,SAASA,qBAAqB,GAAE", "ignoreList": [0]}