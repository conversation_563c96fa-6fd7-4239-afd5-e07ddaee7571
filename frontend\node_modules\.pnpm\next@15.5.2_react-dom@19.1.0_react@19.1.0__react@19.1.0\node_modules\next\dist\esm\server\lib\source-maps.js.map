{"version": 3, "sources": ["../../../src/server/lib/source-maps.ts"], "sourcesContent": ["import type { SourceMap } from 'module'\nimport { LRUCache } from './lru-cache'\n\nfunction noSourceMap(): SourceMap | undefined {\n  return undefined\n}\n\n// Edge runtime does not implement `module`\nconst nativeFindSourceMap =\n  process.env.NEXT_RUNTIME === 'edge'\n    ? noSourceMap\n    : (require('module') as typeof import('module')).findSourceMap\n\n/**\n * https://tc39.es/source-map/#index-map\n */\ninterface IndexSourceMapSection {\n  offset: {\n    line: number\n    column: number\n  }\n  map: BasicSourceMapPayload\n}\n\n// TODO(veil): Upstream types\n/** https://tc39.es/ecma426/#sec-index-source-map */\ninterface IndexSourceMap {\n  version: number\n  file: string\n  sections: IndexSourceMapSection[]\n}\n\n/** https://tc39.es/ecma426/#sec-source-map-format */\nexport interface BasicSourceMapPayload {\n  version: number\n  // TODO: Move to https://github.com/jridgewell/sourcemaps which is actively maintained\n  /** WARNING: `file` is optional. */\n  file: string\n  sourceRoot?: string\n  // TODO: Move to https://github.com/jridgewell/sourcemaps which is actively maintained\n  /** WARNING: `sources[number]` can be `null`. */\n  sources: Array<string>\n  names: Array<string>\n  mappings: string\n  ignoreList?: number[]\n}\n\nexport type ModernSourceMapPayload = BasicSourceMapPayload | IndexSourceMap\n\nexport function sourceMapIgnoreListsEverything(\n  sourceMap: BasicSourceMapPayload\n): boolean {\n  return (\n    sourceMap.ignoreList !== undefined &&\n    sourceMap.sources.length === sourceMap.ignoreList.length\n  )\n}\n\n/**\n * Finds the sourcemap payload applicable to a given frame.\n * Equal to the input unless an Index Source Map is used.\n * @param line0 - The line number of the frame, 0-based.\n * @param column0 - The column number of the frame, 0-based.\n */\nexport function findApplicableSourceMapPayload(\n  line0: number,\n  column0: number,\n  payload: ModernSourceMapPayload\n): BasicSourceMapPayload | undefined {\n  if ('sections' in payload) {\n    if (payload.sections.length === 0) {\n      return undefined\n    }\n\n    // Sections must not overlap and must be sorted: https://tc39.es/source-map/#section-object\n    // Therefore the last section that has an offset less than or equal to the frame is the applicable one.\n    const sections = payload.sections\n    let left = 0\n    let right = sections.length - 1\n    let result: IndexSourceMapSection | null = null\n\n    while (left <= right) {\n      // fast Math.floor\n      const middle = ~~((left + right) / 2)\n      const section = sections[middle]\n      const offset = section.offset\n\n      if (\n        offset.line < line0 ||\n        (offset.line === line0 && offset.column <= column0)\n      ) {\n        result = section\n        left = middle + 1\n      } else {\n        right = middle - 1\n      }\n    }\n\n    return result === null ? undefined : result.map\n  } else {\n    return payload\n  }\n}\n\nconst didWarnAboutInvalidSourceMapDEV = new Set<string>()\n\nconst findSourceMap: (scriptNameOrSourceURL: string) => SourceMap | undefined =\n  process.env.NEXT_RUNTIME === 'nodejs' &&\n  process.versions.node?.startsWith('18')\n    ? // Node.js 18 has a horribly slow `findSourceMap` implementation\n      noSourceMap\n    : nativeFindSourceMap\n\nexport function filterStackFrameDEV(\n  sourceURL: string,\n  functionName: string,\n  line1: number,\n  column1: number\n): boolean {\n  if (sourceURL === '') {\n    // The default implementation filters out <anonymous> stack frames\n    // but we want to retain them because current Server Components and\n    // built-in Components in parent stacks don't have source location.\n    // Filter out frames that show up in Promises to get good names in React's\n    // Server Request track until we come up with a better heuristic.\n    return functionName !== 'new Promise'\n  }\n  if (sourceURL.startsWith('node:') || sourceURL.includes('node_modules')) {\n    return false\n  }\n  try {\n    // Node.js loads source maps eagerly so this call is cheap.\n    // TODO: ESM sourcemaps are O(1) but CommonJS sourcemaps are O(Number of CJS modules).\n    // Make sure this doesn't adversely affect performance when CJS is used by Next.js.\n    const sourceMap = findSourceMap(sourceURL)\n    if (sourceMap === undefined) {\n      // No source map assoicated.\n      // TODO: Node.js types should reflect that `findSourceMap` can return `undefined`.\n      return true\n    }\n    const sourceMapPayload = findApplicableSourceMapPayload(\n      line1 - 1,\n      column1 - 1,\n      sourceMap.payload\n    )\n    if (sourceMapPayload === undefined) {\n      // No source map section applicable to the frame.\n      return true\n    }\n    return !sourceMapIgnoreListsEverything(sourceMapPayload)\n  } catch (cause) {\n    if (process.env.NODE_ENV !== 'production') {\n      // TODO: Share cache with patch-error-inspect\n      if (!didWarnAboutInvalidSourceMapDEV.has(sourceURL)) {\n        didWarnAboutInvalidSourceMapDEV.add(sourceURL)\n        // We should not log an actual error instance here because that will re-enter\n        // this codepath during error inspection and could lead to infinite recursion.\n        console.error(\n          `${sourceURL}: Invalid source map. Only conformant source maps can be used to filter stack frames. Cause: ${cause}`\n        )\n      }\n    }\n\n    return true\n  }\n}\n\nconst invalidSourceMap = Symbol('invalid-source-map')\nconst sourceMapURLs = new LRUCache<string | typeof invalidSourceMap>(\n  512 * 1024 * 1024,\n  (url) =>\n    url === invalidSourceMap\n      ? // Ideally we'd account for key length. So we just guestimate a small source map\n        // so that we don't create a huge cache with empty source maps.\n        8 * 1024\n      : // these URLs contain only ASCII characters so .length is equal to Buffer.byteLength\n        url.length\n)\nexport function findSourceMapURLDEV(\n  scriptNameOrSourceURL: string\n): string | null {\n  let sourceMapURL = sourceMapURLs.get(scriptNameOrSourceURL)\n  if (sourceMapURL === undefined) {\n    let sourceMapPayload: ModernSourceMapPayload | undefined\n    try {\n      sourceMapPayload = findSourceMap(scriptNameOrSourceURL)?.payload\n    } catch (cause) {\n      console.error(\n        `${scriptNameOrSourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n    }\n\n    if (sourceMapPayload === undefined) {\n      sourceMapURL = invalidSourceMap\n    } else {\n      // TODO: Might be more efficient to extract the relevant section from Index Maps.\n      // Unclear if that search is worth the smaller payload we have to stringify.\n      const sourceMapJSON = JSON.stringify(sourceMapPayload)\n      const sourceMapURLData = Buffer.from(sourceMapJSON, 'utf8').toString(\n        'base64'\n      )\n      sourceMapURL = `data:application/json;base64,${sourceMapURLData}`\n    }\n\n    sourceMapURLs.set(scriptNameOrSourceURL, sourceMapURL)\n  }\n\n  return sourceMapURL === invalidSourceMap ? null : sourceMapURL\n}\n\nexport function devirtualizeReactServerURL(sourceURL: string): string {\n  if (sourceURL.startsWith('about://React/')) {\n    // about://React/Server/file://<filename>?42 => file://<filename>\n    const envIdx = sourceURL.indexOf('/', 'about://React/'.length)\n    const suffixIdx = sourceURL.lastIndexOf('?')\n    if (envIdx > -1 && suffixIdx > -1) {\n      return decodeURI(sourceURL.slice(envIdx + 1, suffixIdx))\n    }\n  }\n  return sourceURL\n}\n\nfunction isAnonymousFrameLikelyJSNative(methodName: string): boolean {\n  // Anonymous frames can also be produced in React parent stacks either from\n  // host components or Server Components. We don't want to ignore those.\n  // This could hide user-space methods that are named like native JS methods but\n  // should you really do that?\n  return (\n    // e.g. JSON.parse\n    methodName.startsWith('JSON.') ||\n    // E.g. Promise.withResolves\n    methodName.startsWith('Function.') ||\n    // various JS built-ins\n    methodName.startsWith('Promise.') ||\n    methodName.startsWith('Array.') ||\n    methodName.startsWith('Set.') ||\n    methodName.startsWith('Map.')\n  )\n}\n\nexport function ignoreListAnonymousStackFramesIfSandwiched<Frame>(\n  frames: Frame[],\n  isAnonymousFrame: (frame: Frame) => boolean,\n  isIgnoredFrame: (frame: Frame) => boolean,\n  getMethodName: (frame: Frame) => string,\n  /** only passes frames for which `isAnonymousFrame` and their method is a native JS method or `isIgnoredFrame` return true */\n  ignoreFrame: (frame: Frame) => void\n): void {\n  for (let i = 1; i < frames.length; i++) {\n    const currentFrame = frames[i]\n    if (\n      !(\n        isAnonymousFrame(currentFrame) &&\n        isAnonymousFrameLikelyJSNative(getMethodName(currentFrame))\n      )\n    ) {\n      continue\n    }\n\n    const previousFrameIsIgnored = isIgnoredFrame(frames[i - 1])\n    if (previousFrameIsIgnored && i < frames.length - 1) {\n      let ignoreSandwich = false\n      let j = i + 1\n      for (j; j < frames.length; j++) {\n        const nextFrame = frames[j]\n        const nextFrameIsAnonymous =\n          isAnonymousFrame(nextFrame) &&\n          isAnonymousFrameLikelyJSNative(getMethodName(nextFrame))\n        if (nextFrameIsAnonymous) {\n          continue\n        }\n\n        const nextFrameIsIgnored = isIgnoredFrame(nextFrame)\n        if (nextFrameIsIgnored) {\n          ignoreSandwich = true\n          break\n        }\n      }\n\n      if (ignoreSandwich) {\n        for (i; i < j; i++) {\n          ignoreFrame(frames[i])\n        }\n      }\n    }\n  }\n}\n"], "names": ["process", "L<PERSON><PERSON><PERSON>", "noSourceMap", "undefined", "nativeFindSourceMap", "env", "NEXT_RUNTIME", "require", "findSourceMap", "sourceMapIgnoreListsEverything", "sourceMap", "ignoreList", "sources", "length", "findApplicableSourceMapPayload", "line0", "column0", "payload", "sections", "left", "right", "result", "middle", "section", "offset", "line", "column", "map", "didWarnAboutInvalidSourceMapDEV", "Set", "versions", "node", "startsWith", "filterStackFrameDEV", "sourceURL", "functionName", "line1", "column1", "includes", "sourceMapPayload", "cause", "NODE_ENV", "has", "add", "console", "error", "invalidSourceMap", "Symbol", "sourceMapURLs", "url", "findSourceMapURLDEV", "scriptNameOrSourceURL", "sourceMapURL", "get", "sourceMapJSON", "JSON", "stringify", "sourceMapURLData", "<PERSON><PERSON><PERSON>", "from", "toString", "set", "devirtualizeReactServerURL", "envIdx", "indexOf", "suffixIdx", "lastIndexOf", "decodeURI", "slice", "isAnonymousFrameLikelyJSNative", "methodName", "ignoreListAnonymousStackFramesIfSandwiched", "frames", "isAnonymousFrame", "isIgnoredFrame", "getMethodName", "ignore<PERSON><PERSON><PERSON>", "i", "currentFrame", "previousFrameIsIgnored", "ignoreSandwich", "j", "next<PERSON><PERSON><PERSON>", "nextFrameIsAnonymous", "nextFrameIsIgnored"], "mappings": "IA4GEA;AA3GF,SAASC,QAAQ,QAAQ,cAAa;AAEtC,SAASC;IACP,OAAOC;AACT;AAEA,2CAA2C;AAC3C,MAAMC,sBACJJ,QAAQK,GAAG,CAACC,YAAY,KAAK,SACzBJ,cACA,AAACK,QAAQ,UAAsCC,aAAa;AAsClE,OAAO,SAASC,+BACdC,SAAgC;IAEhC,OACEA,UAAUC,UAAU,KAAKR,aACzBO,UAAUE,OAAO,CAACC,MAAM,KAAKH,UAAUC,UAAU,CAACE,MAAM;AAE5D;AAEA;;;;;CAKC,GACD,OAAO,SAASC,+BACdC,KAAa,EACbC,OAAe,EACfC,OAA+B;IAE/B,IAAI,cAAcA,SAAS;QACzB,IAAIA,QAAQC,QAAQ,CAACL,MAAM,KAAK,GAAG;YACjC,OAAOV;QACT;QAEA,2FAA2F;QAC3F,uGAAuG;QACvG,MAAMe,WAAWD,QAAQC,QAAQ;QACjC,IAAIC,OAAO;QACX,IAAIC,QAAQF,SAASL,MAAM,GAAG;QAC9B,IAAIQ,SAAuC;QAE3C,MAAOF,QAAQC,MAAO;YACpB,kBAAkB;YAClB,MAAME,SAAS,CAAC,CAAE,CAAA,AAACH,CAAAA,OAAOC,KAAI,IAAK,CAAA;YACnC,MAAMG,UAAUL,QAAQ,CAACI,OAAO;YAChC,MAAME,SAASD,QAAQC,MAAM;YAE7B,IACEA,OAAOC,IAAI,GAAGV,SACbS,OAAOC,IAAI,KAAKV,SAASS,OAAOE,MAAM,IAAIV,SAC3C;gBACAK,SAASE;gBACTJ,OAAOG,SAAS;YAClB,OAAO;gBACLF,QAAQE,SAAS;YACnB;QACF;QAEA,OAAOD,WAAW,OAAOlB,YAAYkB,OAAOM,GAAG;IACjD,OAAO;QACL,OAAOV;IACT;AACF;AAEA,MAAMW,kCAAkC,IAAIC;AAE5C,MAAMrB,gBACJR,QAAQK,GAAG,CAACC,YAAY,KAAK,cAC7BN,yBAAAA,QAAQ8B,QAAQ,CAACC,IAAI,qBAArB/B,uBAAuBgC,UAAU,CAAC,SAE9B9B,cACAE;AAEN,OAAO,SAAS6B,oBACdC,SAAiB,EACjBC,YAAoB,EACpBC,KAAa,EACbC,OAAe;IAEf,IAAIH,cAAc,IAAI;QACpB,kEAAkE;QAClE,mEAAmE;QACnE,mEAAmE;QACnE,0EAA0E;QAC1E,iEAAiE;QACjE,OAAOC,iBAAiB;IAC1B;IACA,IAAID,UAAUF,UAAU,CAAC,YAAYE,UAAUI,QAAQ,CAAC,iBAAiB;QACvE,OAAO;IACT;IACA,IAAI;QACF,2DAA2D;QAC3D,sFAAsF;QACtF,mFAAmF;QACnF,MAAM5B,YAAYF,cAAc0B;QAChC,IAAIxB,cAAcP,WAAW;YAC3B,4BAA4B;YAC5B,kFAAkF;YAClF,OAAO;QACT;QACA,MAAMoC,mBAAmBzB,+BACvBsB,QAAQ,GACRC,UAAU,GACV3B,UAAUO,OAAO;QAEnB,IAAIsB,qBAAqBpC,WAAW;YAClC,iDAAiD;YACjD,OAAO;QACT;QACA,OAAO,CAACM,+BAA+B8B;IACzC,EAAE,OAAOC,OAAO;QACd,IAAIxC,QAAQK,GAAG,CAACoC,QAAQ,KAAK,cAAc;YACzC,6CAA6C;YAC7C,IAAI,CAACb,gCAAgCc,GAAG,CAACR,YAAY;gBACnDN,gCAAgCe,GAAG,CAACT;gBACpC,6EAA6E;gBAC7E,8EAA8E;gBAC9EU,QAAQC,KAAK,CACX,GAAGX,UAAU,6FAA6F,EAAEM,OAAO;YAEvH;QACF;QAEA,OAAO;IACT;AACF;AAEA,MAAMM,mBAAmBC,OAAO;AAChC,MAAMC,gBAAgB,IAAI/C,SACxB,MAAM,OAAO,MACb,CAACgD,MACCA,QAAQH,mBAEJ,+DAA+D;IAC/D,IAAI,OAEJG,IAAIpC,MAAM;AAElB,OAAO,SAASqC,oBACdC,qBAA6B;IAE7B,IAAIC,eAAeJ,cAAcK,GAAG,CAACF;IACrC,IAAIC,iBAAiBjD,WAAW;QAC9B,IAAIoC;QACJ,IAAI;gBACiB/B;YAAnB+B,oBAAmB/B,iBAAAA,cAAc2C,2CAAd3C,eAAsCS,OAAO;QAClE,EAAE,OAAOuB,OAAO;YACdI,QAAQC,KAAK,CACX,GAAGM,sBAAsB,gGAAgG,EAAEX,OAAO;QAEtI;QAEA,IAAID,qBAAqBpC,WAAW;YAClCiD,eAAeN;QACjB,OAAO;YACL,iFAAiF;YACjF,4EAA4E;YAC5E,MAAMQ,gBAAgBC,KAAKC,SAAS,CAACjB;YACrC,MAAMkB,mBAAmBC,OAAOC,IAAI,CAACL,eAAe,QAAQM,QAAQ,CAClE;YAEFR,eAAe,CAAC,6BAA6B,EAAEK,kBAAkB;QACnE;QAEAT,cAAca,GAAG,CAACV,uBAAuBC;IAC3C;IAEA,OAAOA,iBAAiBN,mBAAmB,OAAOM;AACpD;AAEA,OAAO,SAASU,2BAA2B5B,SAAiB;IAC1D,IAAIA,UAAUF,UAAU,CAAC,mBAAmB;QAC1C,iEAAiE;QACjE,MAAM+B,SAAS7B,UAAU8B,OAAO,CAAC,KAAK,iBAAiBnD,MAAM;QAC7D,MAAMoD,YAAY/B,UAAUgC,WAAW,CAAC;QACxC,IAAIH,SAAS,CAAC,KAAKE,YAAY,CAAC,GAAG;YACjC,OAAOE,UAAUjC,UAAUkC,KAAK,CAACL,SAAS,GAAGE;QAC/C;IACF;IACA,OAAO/B;AACT;AAEA,SAASmC,+BAA+BC,UAAkB;IACxD,2EAA2E;IAC3E,uEAAuE;IACvE,+EAA+E;IAC/E,6BAA6B;IAC7B,OACE,kBAAkB;IAClBA,WAAWtC,UAAU,CAAC,YACtB,4BAA4B;IAC5BsC,WAAWtC,UAAU,CAAC,gBACtB,uBAAuB;IACvBsC,WAAWtC,UAAU,CAAC,eACtBsC,WAAWtC,UAAU,CAAC,aACtBsC,WAAWtC,UAAU,CAAC,WACtBsC,WAAWtC,UAAU,CAAC;AAE1B;AAEA,OAAO,SAASuC,2CACdC,MAAe,EACfC,gBAA2C,EAC3CC,cAAyC,EACzCC,aAAuC,EACvC,2HAA2H,GAC3HC,WAAmC;IAEnC,IAAK,IAAIC,IAAI,GAAGA,IAAIL,OAAO3D,MAAM,EAAEgE,IAAK;QACtC,MAAMC,eAAeN,MAAM,CAACK,EAAE;QAC9B,IACE,CACEJ,CAAAA,iBAAiBK,iBACjBT,+BAA+BM,cAAcG,cAAa,GAE5D;YACA;QACF;QAEA,MAAMC,yBAAyBL,eAAeF,MAAM,CAACK,IAAI,EAAE;QAC3D,IAAIE,0BAA0BF,IAAIL,OAAO3D,MAAM,GAAG,GAAG;YACnD,IAAImE,iBAAiB;YACrB,IAAIC,IAAIJ,IAAI;YACZ,IAAKI,GAAGA,IAAIT,OAAO3D,MAAM,EAAEoE,IAAK;gBAC9B,MAAMC,YAAYV,MAAM,CAACS,EAAE;gBAC3B,MAAME,uBACJV,iBAAiBS,cACjBb,+BAA+BM,cAAcO;gBAC/C,IAAIC,sBAAsB;oBACxB;gBACF;gBAEA,MAAMC,qBAAqBV,eAAeQ;gBAC1C,IAAIE,oBAAoB;oBACtBJ,iBAAiB;oBACjB;gBACF;YACF;YAEA,IAAIA,gBAAgB;gBAClB,IAAKH,GAAGA,IAAII,GAAGJ,IAAK;oBAClBD,YAAYJ,MAAM,CAACK,EAAE;gBACvB;YACF;QACF;IACF;AACF", "ignoreList": [0]}