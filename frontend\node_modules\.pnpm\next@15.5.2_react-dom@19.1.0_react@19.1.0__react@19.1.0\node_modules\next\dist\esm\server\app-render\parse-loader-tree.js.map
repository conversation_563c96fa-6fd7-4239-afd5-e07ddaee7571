{"version": 3, "sources": ["../../../src/server/app-render/parse-loader-tree.ts"], "sourcesContent": ["import { DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { LoaderTree } from '../lib/app-dir-module'\n\nexport function parseLoaderTree(tree: LoaderTree) {\n  const [segment, parallelRoutes, modules] = tree\n  const { layout, template } = modules\n  let { page } = modules\n  // a __DEFAULT__ segment means that this route didn't match any of the\n  // segments in the route, so we should use the default page\n  page = segment === DEFAULT_SEGMENT_KEY ? modules.defaultPage : page\n\n  const conventionPath = layout?.[1] || template?.[1] || page?.[1]\n\n  return {\n    page,\n    segment,\n    modules,\n    /* it can be either layout / template / page */\n    conventionPath,\n    parallelRoutes,\n  }\n}\n"], "names": ["DEFAULT_SEGMENT_KEY", "parseLoaderTree", "tree", "segment", "parallelRoutes", "modules", "layout", "template", "page", "defaultPage", "conventionPath"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAG9D,OAAO,SAASC,gBAAgBC,IAAgB;IAC9C,MAAM,CAACC,SAASC,gBAAgBC,QAAQ,GAAGH;IAC3C,MAAM,EAAEI,MAAM,EAAEC,QAAQ,EAAE,GAAGF;IAC7B,IAAI,EAAEG,IAAI,EAAE,GAAGH;IACf,sEAAsE;IACtE,2DAA2D;IAC3DG,OAAOL,YAAYH,sBAAsBK,QAAQI,WAAW,GAAGD;IAE/D,MAAME,iBAAiBJ,CAAAA,0BAAAA,MAAQ,CAAC,EAAE,MAAIC,4BAAAA,QAAU,CAAC,EAAE,MAAIC,wBAAAA,IAAM,CAAC,EAAE;IAEhE,OAAO;QACLA;QACAL;QACAE;QACA,6CAA6C,GAC7CK;QACAN;IACF;AACF", "ignoreList": [0]}