{"version": 3, "sources": ["../../../src/server/lib/mock-request.ts"], "sourcesContent": ["import type {\n  ServerResponse,\n  OutgoingHttpHeaders,\n  OutgoingHttpHeader,\n  IncomingMessage,\n  IncomingHttpHeaders,\n} from 'http'\nimport type { Socket } from 'net'\nimport type { TLSSocket } from 'tls'\n\nimport Stream from 'stream'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  toNodeOutgoingHttpHeaders,\n} from '../web/utils'\n\ninterface MockedRequestOptions {\n  url: string\n  headers: IncomingHttpHeaders\n  method: string\n  readable?: Stream.Readable\n  socket?: Socket | null\n}\n\nexport class MockedRequest extends Stream.Readable implements IncomingMessage {\n  public url: string\n  public readonly statusCode?: number | undefined\n  public readonly statusMessage?: string | undefined\n  public readonly headers: IncomingHttpHeaders\n  public readonly method: string\n\n  // This is hardcoded for now, but can be updated to be configurable if needed.\n  public readonly httpVersion = '1.0'\n  public readonly httpVersionMajor = 1\n  public readonly httpVersionMinor = 0\n\n  private bodyReadable?: Stream.Readable\n\n  // If we don't actually have a socket, we'll just use a mock one that\n  // always returns false for the `encrypted` property and undefined for the\n  // `remoteAddress` property.\n  public socket: Socket = new Proxy<TLSSocket>({} as TLSSocket, {\n    get: (_target, prop) => {\n      if (prop !== 'encrypted' && prop !== 'remoteAddress') {\n        throw new Error('Method not implemented')\n      }\n\n      if (prop === 'remoteAddress') return undefined\n      // For this mock request, always ensure we just respond with the encrypted\n      // set to false to ensure there's no odd leakages.\n      return false\n    },\n  })\n\n  constructor({\n    url,\n    headers,\n    method,\n    socket = null,\n    readable,\n  }: MockedRequestOptions) {\n    super()\n\n    this.url = url\n    this.headers = headers\n    this.method = method\n\n    if (readable) {\n      this.bodyReadable = readable\n      this.bodyReadable.on('end', () => this.emit('end'))\n      this.bodyReadable.on('close', () => this.emit('close'))\n    }\n\n    if (socket) {\n      this.socket = socket\n    }\n  }\n\n  public get headersDistinct(): NodeJS.Dict<string[]> {\n    const headers: NodeJS.Dict<string[]> = {}\n    for (const [key, value] of Object.entries(this.headers)) {\n      if (!value) continue\n\n      headers[key] = Array.isArray(value) ? value : [value]\n    }\n\n    return headers\n  }\n\n  public _read(size: number): void {\n    if (this.bodyReadable) {\n      return this.bodyReadable._read(size)\n    } else {\n      this.emit('end')\n      this.emit('close')\n    }\n  }\n\n  /**\n   * The `connection` property is just an alias for the `socket` property.\n   *\n   * @deprecated — since v13.0.0 - Use socket instead.\n   */\n  public get connection(): Socket {\n    return this.socket\n  }\n\n  // The following methods are not implemented as they are not used in the\n  // Next.js codebase.\n\n  public get aborted(): boolean {\n    throw new Error('Method not implemented')\n  }\n\n  public get complete(): boolean {\n    throw new Error('Method not implemented')\n  }\n\n  public get trailers(): NodeJS.Dict<string> {\n    throw new Error('Method not implemented')\n  }\n\n  public get trailersDistinct(): NodeJS.Dict<string[]> {\n    throw new Error('Method not implemented')\n  }\n\n  public get rawTrailers(): string[] {\n    throw new Error('Method not implemented')\n  }\n\n  public get rawHeaders(): string[] {\n    throw new Error('Method not implemented.')\n  }\n\n  public setTimeout(): this {\n    throw new Error('Method not implemented.')\n  }\n}\n\nexport interface MockedResponseOptions {\n  statusCode?: number\n  socket?: Socket | null\n  headers?: OutgoingHttpHeaders\n  resWriter?: (chunk: Uint8Array | Buffer | string) => boolean\n}\n\nexport class MockedResponse extends Stream.Writable implements ServerResponse {\n  public statusCode: number\n  public statusMessage: string = ''\n  public finished = false\n  public headersSent = false\n  public readonly socket: Socket | null\n\n  /**\n   * A promise that resolves to `true` when the response has been streamed.\n   *\n   * @internal - used internally by Next.js\n   */\n  public readonly hasStreamed: Promise<boolean>\n\n  /**\n   * A list of buffers that have been written to the response.\n   *\n   * @internal - used internally by Next.js\n   */\n  public readonly buffers: Buffer[] = []\n\n  /**\n   * The headers object that contains the headers that were initialized on the\n   * response and any that were added subsequently.\n   *\n   * @internal - used internally by Next.js\n   */\n  public readonly headers: Headers\n\n  private resWriter: MockedResponseOptions['resWriter']\n\n  public readonly headPromise: Promise<void>\n  private headPromiseResolve?: () => void\n\n  constructor(res: MockedResponseOptions = {}) {\n    super()\n\n    this.statusCode = res.statusCode ?? 200\n    this.socket = res.socket ?? null\n    this.headers = res.headers\n      ? fromNodeOutgoingHttpHeaders(res.headers)\n      : new Headers()\n\n    this.headPromise = new Promise<void>((resolve) => {\n      this.headPromiseResolve = resolve\n    })\n\n    // Attach listeners for the `finish`, `end`, and `error` events to the\n    // `MockedResponse` instance.\n    this.hasStreamed = new Promise<boolean>((resolve, reject) => {\n      this.on('finish', () => resolve(true))\n      this.on('end', () => resolve(true))\n      this.on('error', (err) => reject(err))\n    }).then((val) => {\n      this.headPromiseResolve?.()\n      return val\n    })\n\n    if (res.resWriter) {\n      this.resWriter = res.resWriter\n    }\n  }\n\n  public appendHeader(name: string, value: string | string[]): this {\n    const values = Array.isArray(value) ? value : [value]\n    for (const v of values) {\n      this.headers.append(name, v)\n    }\n\n    return this\n  }\n\n  /**\n   * Returns true if the response has been sent, false otherwise.\n   *\n   * @internal - used internally by Next.js\n   */\n  public get isSent() {\n    return this.finished || this.headersSent\n  }\n\n  /**\n   * The `connection` property is just an alias for the `socket` property.\n   *\n   * @deprecated — since v13.0.0 - Use socket instead.\n   */\n  public get connection(): Socket | null {\n    return this.socket\n  }\n\n  public write(chunk: Uint8Array | Buffer | string) {\n    if (this.resWriter) {\n      return this.resWriter(chunk)\n    }\n    this.buffers.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk))\n\n    return true\n  }\n\n  public end() {\n    this.finished = true\n    return super.end(...arguments)\n  }\n\n  /**\n   * This method is a no-op because the `MockedResponse` instance is not\n   * actually connected to a socket. This method is not specified on the\n   * interface type for `ServerResponse` but is called by Node.js.\n   *\n   * @see https://github.com/nodejs/node/pull/7949\n   */\n  public _implicitHeader() {}\n\n  public _write(\n    chunk: Buffer | string,\n    _encoding: string,\n    callback: () => void\n  ) {\n    this.write(chunk)\n\n    // According to Node.js documentation, the callback MUST be invoked to\n    // signal that the write completed successfully. If this callback is not\n    // invoked, the 'finish' event will not be emitted.\n    //\n    // https://nodejs.org/docs/latest-v16.x/api/stream.html#writable_writechunk-encoding-callback\n    callback()\n  }\n\n  public writeHead(\n    statusCode: number,\n    statusMessage?: string | undefined,\n    headers?: OutgoingHttpHeaders | OutgoingHttpHeader[] | undefined\n  ): this\n  public writeHead(\n    statusCode: number,\n    headers?: OutgoingHttpHeaders | OutgoingHttpHeader[] | undefined\n  ): this\n  public writeHead(\n    statusCode: number,\n    statusMessage?:\n      | string\n      | OutgoingHttpHeaders\n      | OutgoingHttpHeader[]\n      | undefined,\n    headers?: OutgoingHttpHeaders | OutgoingHttpHeader[] | undefined\n  ): this {\n    if (!headers && typeof statusMessage !== 'string') {\n      headers = statusMessage\n    } else if (typeof statusMessage === 'string' && statusMessage.length > 0) {\n      this.statusMessage = statusMessage\n    }\n\n    if (headers) {\n      // When headers have been set with response.setHeader(), they will be\n      // merged with any headers passed to response.writeHead(), with the\n      // headers passed to response.writeHead() given precedence.\n      //\n      // https://nodejs.org/api/http.html#responsewriteheadstatuscode-statusmessage-headers\n      //\n      // For this reason, we need to only call `set` to ensure that this will\n      // overwrite any existing headers.\n      if (Array.isArray(headers)) {\n        // headers may be an Array where the keys and values are in the same list.\n        // It is not a list of tuples. So, the even-numbered offsets are key\n        // values, and the odd-numbered offsets are the associated values. The\n        // array is in the same format as request.rawHeaders.\n        for (let i = 0; i < headers.length; i += 2) {\n          // The header key is always a string according to the spec.\n          this.setHeader(headers[i] as string, headers[i + 1])\n        }\n      } else {\n        for (const [key, value] of Object.entries(headers)) {\n          // Skip undefined values\n          if (typeof value === 'undefined') continue\n\n          this.setHeader(key, value)\n        }\n      }\n    }\n\n    this.statusCode = statusCode\n    this.headersSent = true\n    this.headPromiseResolve?.()\n\n    return this\n  }\n\n  public hasHeader(name: string): boolean {\n    return this.headers.has(name)\n  }\n\n  public getHeader(name: string): string | undefined {\n    return this.headers.get(name) ?? undefined\n  }\n\n  public getHeaders(): OutgoingHttpHeaders {\n    return toNodeOutgoingHttpHeaders(this.headers)\n  }\n\n  public getHeaderNames(): string[] {\n    return Array.from(this.headers.keys())\n  }\n\n  public setHeader(name: string, value: OutgoingHttpHeader) {\n    if (Array.isArray(value)) {\n      // Because `set` here should override any existing values, we need to\n      // delete the existing values before setting the new ones via `append`.\n      this.headers.delete(name)\n\n      for (const v of value) {\n        this.headers.append(name, v)\n      }\n    } else if (typeof value === 'number') {\n      this.headers.set(name, value.toString())\n    } else {\n      this.headers.set(name, value)\n    }\n\n    return this\n  }\n\n  public removeHeader(name: string): void {\n    this.headers.delete(name)\n  }\n\n  public flushHeaders(): void {\n    // This is a no-op because we don't actually have a socket to flush the\n    // headers to.\n  }\n\n  // The following methods are not implemented as they are not used in the\n  // Next.js codebase.\n\n  public get strictContentLength(): boolean {\n    throw new Error('Method not implemented.')\n  }\n\n  public writeEarlyHints() {\n    throw new Error('Method not implemented.')\n  }\n\n  public get req(): IncomingMessage {\n    throw new Error('Method not implemented.')\n  }\n\n  public assignSocket() {\n    throw new Error('Method not implemented.')\n  }\n\n  public detachSocket(): void {\n    throw new Error('Method not implemented.')\n  }\n\n  public writeContinue(): void {\n    throw new Error('Method not implemented.')\n  }\n\n  public writeProcessing(): void {\n    throw new Error('Method not implemented.')\n  }\n\n  public get upgrading(): boolean {\n    throw new Error('Method not implemented.')\n  }\n\n  public get chunkedEncoding(): boolean {\n    throw new Error('Method not implemented.')\n  }\n\n  public get shouldKeepAlive(): boolean {\n    throw new Error('Method not implemented.')\n  }\n\n  public get useChunkedEncodingByDefault(): boolean {\n    throw new Error('Method not implemented.')\n  }\n\n  public get sendDate(): boolean {\n    throw new Error('Method not implemented.')\n  }\n\n  public setTimeout(): this {\n    throw new Error('Method not implemented.')\n  }\n\n  public addTrailers(): void {\n    throw new Error('Method not implemented.')\n  }\n\n  public setHeaders(): this {\n    throw new Error('Method not implemented.')\n  }\n}\n\ninterface RequestResponseMockerOptions {\n  url: string\n  headers?: IncomingHttpHeaders\n  method?: string\n  bodyReadable?: Stream.Readable\n  resWriter?: (chunk: Uint8Array | Buffer | string) => boolean\n  socket?: Socket | null\n}\n\nexport function createRequestResponseMocks({\n  url,\n  headers = {},\n  method = 'GET',\n  bodyReadable,\n  resWriter,\n  socket = null,\n}: RequestResponseMockerOptions) {\n  return {\n    req: new MockedRequest({\n      url,\n      headers,\n      method,\n      socket,\n      readable: bodyReadable,\n    }),\n    res: new MockedResponse({ socket, resWriter }),\n  }\n}\n"], "names": ["Stream", "fromNodeOutgoingHttpHeaders", "toNodeOutgoingHttpHeaders", "MockedRequest", "Readable", "constructor", "url", "headers", "method", "socket", "readable", "httpVersion", "httpVersionMajor", "httpVersionMinor", "Proxy", "get", "_target", "prop", "Error", "undefined", "bodyReadable", "on", "emit", "headersDistinct", "key", "value", "Object", "entries", "Array", "isArray", "_read", "size", "connection", "aborted", "complete", "trailers", "trailersDistinct", "rawTrailers", "rawHeaders", "setTimeout", "MockedResponse", "Writable", "res", "statusMessage", "finished", "headersSent", "buffers", "statusCode", "Headers", "head<PERSON><PERSON><PERSON>", "Promise", "resolve", "headPromiseResolve", "hasStreamed", "reject", "err", "then", "val", "resWriter", "append<PERSON><PERSON>er", "name", "values", "v", "append", "isSent", "write", "chunk", "push", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "end", "arguments", "_implicitHeader", "_write", "_encoding", "callback", "writeHead", "length", "i", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "has", "<PERSON><PERSON><PERSON><PERSON>", "getHeaders", "getHeaderNames", "keys", "delete", "set", "toString", "removeHeader", "flushHeaders", "strictContent<PERSON>ength", "writeEarlyHints", "req", "assignSocket", "detachSocket", "writeContinue", "writeProcessing", "upgrading", "chunkedEncoding", "shouldKeepAlive", "useChunkedEncodingByDefault", "sendDate", "addTrailers", "setHeaders", "createRequestResponseMocks"], "mappings": "AAUA,OAAOA,YAAY,SAAQ;AAC3B,SACEC,2BAA2B,EAC3BC,yBAAyB,QACpB,eAAc;AAUrB,OAAO,MAAMC,sBAAsBH,OAAOI,QAAQ;IA8BhDC,YAAY,EACVC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,SAAS,IAAI,EACbC,QAAQ,EACa,CAAE;QACvB,KAAK,IA9BP,8EAA8E;aAC9DC,cAAc,YACdC,mBAAmB,QACnBC,mBAAmB,GAInC,qEAAqE;QACrE,0EAA0E;QAC1E,4BAA4B;aACrBJ,SAAiB,IAAIK,MAAiB,CAAC,GAAgB;YAC5DC,KAAK,CAACC,SAASC;gBACb,IAAIA,SAAS,eAAeA,SAAS,iBAAiB;oBACpD,MAAM,qBAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAkC;gBAC1C;gBAEA,IAAID,SAAS,iBAAiB,OAAOE;gBACrC,0EAA0E;gBAC1E,kDAAkD;gBAClD,OAAO;YACT;QACF;QAWE,IAAI,CAACb,GAAG,GAAGA;QACX,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QAEd,IAAIE,UAAU;YACZ,IAAI,CAACU,YAAY,GAAGV;YACpB,IAAI,CAACU,YAAY,CAACC,EAAE,CAAC,OAAO,IAAM,IAAI,CAACC,IAAI,CAAC;YAC5C,IAAI,CAACF,YAAY,CAACC,EAAE,CAAC,SAAS,IAAM,IAAI,CAACC,IAAI,CAAC;QAChD;QAEA,IAAIb,QAAQ;YACV,IAAI,CAACA,MAAM,GAAGA;QAChB;IACF;IAEA,IAAWc,kBAAyC;QAClD,MAAMhB,UAAiC,CAAC;QACxC,KAAK,MAAM,CAACiB,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAC,IAAI,CAACpB,OAAO,EAAG;YACvD,IAAI,CAACkB,OAAO;YAEZlB,OAAO,CAACiB,IAAI,GAAGI,MAAMC,OAAO,CAACJ,SAASA,QAAQ;gBAACA;aAAM;QACvD;QAEA,OAAOlB;IACT;IAEOuB,MAAMC,IAAY,EAAQ;QAC/B,IAAI,IAAI,CAACX,YAAY,EAAE;YACrB,OAAO,IAAI,CAACA,YAAY,CAACU,KAAK,CAACC;QACjC,OAAO;YACL,IAAI,CAACT,IAAI,CAAC;YACV,IAAI,CAACA,IAAI,CAAC;QACZ;IACF;IAEA;;;;GAIC,GACD,IAAWU,aAAqB;QAC9B,OAAO,IAAI,CAACvB,MAAM;IACpB;IAEA,wEAAwE;IACxE,oBAAoB;IAEpB,IAAWwB,UAAmB;QAC5B,MAAM,qBAAmC,CAAnC,IAAIf,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAWgB,WAAoB;QAC7B,MAAM,qBAAmC,CAAnC,IAAIhB,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAWiB,WAAgC;QACzC,MAAM,qBAAmC,CAAnC,IAAIjB,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAWkB,mBAA0C;QACnD,MAAM,qBAAmC,CAAnC,IAAIlB,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAWmB,cAAwB;QACjC,MAAM,qBAAmC,CAAnC,IAAInB,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAWoB,aAAuB;QAChC,MAAM,qBAAoC,CAApC,IAAIpB,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEOqB,aAAmB;QACxB,MAAM,qBAAoC,CAApC,IAAIrB,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;AACF;AASA,OAAO,MAAMsB,uBAAuBxC,OAAOyC,QAAQ;IAkCjDpC,YAAYqC,MAA6B,CAAC,CAAC,CAAE;QAC3C,KAAK,SAjCAC,gBAAwB,SACxBC,WAAW,YACXC,cAAc,OAUrB;;;;GAIC,QACeC,UAAoB,EAAE;QAkBpC,IAAI,CAACC,UAAU,GAAGL,IAAIK,UAAU,IAAI;QACpC,IAAI,CAACtC,MAAM,GAAGiC,IAAIjC,MAAM,IAAI;QAC5B,IAAI,CAACF,OAAO,GAAGmC,IAAInC,OAAO,GACtBN,4BAA4ByC,IAAInC,OAAO,IACvC,IAAIyC;QAER,IAAI,CAACC,WAAW,GAAG,IAAIC,QAAc,CAACC;YACpC,IAAI,CAACC,kBAAkB,GAAGD;QAC5B;QAEA,sEAAsE;QACtE,6BAA6B;QAC7B,IAAI,CAACE,WAAW,GAAG,IAAIH,QAAiB,CAACC,SAASG;YAChD,IAAI,CAACjC,EAAE,CAAC,UAAU,IAAM8B,QAAQ;YAChC,IAAI,CAAC9B,EAAE,CAAC,OAAO,IAAM8B,QAAQ;YAC7B,IAAI,CAAC9B,EAAE,CAAC,SAAS,CAACkC,MAAQD,OAAOC;QACnC,GAAGC,IAAI,CAAC,CAACC;YACP,IAAI,CAACL,kBAAkB,oBAAvB,IAAI,CAACA,kBAAkB,MAAvB,IAAI;YACJ,OAAOK;QACT;QAEA,IAAIf,IAAIgB,SAAS,EAAE;YACjB,IAAI,CAACA,SAAS,GAAGhB,IAAIgB,SAAS;QAChC;IACF;IAEOC,aAAaC,IAAY,EAAEnC,KAAwB,EAAQ;QAChE,MAAMoC,SAASjC,MAAMC,OAAO,CAACJ,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,MAAMqC,KAAKD,OAAQ;YACtB,IAAI,CAACtD,OAAO,CAACwD,MAAM,CAACH,MAAME;QAC5B;QAEA,OAAO,IAAI;IACb;IAEA;;;;GAIC,GACD,IAAWE,SAAS;QAClB,OAAO,IAAI,CAACpB,QAAQ,IAAI,IAAI,CAACC,WAAW;IAC1C;IAEA;;;;GAIC,GACD,IAAWb,aAA4B;QACrC,OAAO,IAAI,CAACvB,MAAM;IACpB;IAEOwD,MAAMC,KAAmC,EAAE;QAChD,IAAI,IAAI,CAACR,SAAS,EAAE;YAClB,OAAO,IAAI,CAACA,SAAS,CAACQ;QACxB;QACA,IAAI,CAACpB,OAAO,CAACqB,IAAI,CAACC,OAAOC,QAAQ,CAACH,SAASA,QAAQE,OAAOE,IAAI,CAACJ;QAE/D,OAAO;IACT;IAEOK,MAAM;QACX,IAAI,CAAC3B,QAAQ,GAAG;QAChB,OAAO,KAAK,CAAC2B,OAAOC;IACtB;IAEA;;;;;;GAMC,GACD,AAAOC,kBAAkB,CAAC;IAEnBC,OACLR,KAAsB,EACtBS,SAAiB,EACjBC,QAAoB,EACpB;QACA,IAAI,CAACX,KAAK,CAACC;QAEX,sEAAsE;QACtE,wEAAwE;QACxE,mDAAmD;QACnD,EAAE;QACF,6FAA6F;QAC7FU;IACF;IAWOC,UACL9B,UAAkB,EAClBJ,aAIa,EACbpC,OAAgE,EAC1D;QACN,IAAI,CAACA,WAAW,OAAOoC,kBAAkB,UAAU;YACjDpC,UAAUoC;QACZ,OAAO,IAAI,OAAOA,kBAAkB,YAAYA,cAAcmC,MAAM,GAAG,GAAG;YACxE,IAAI,CAACnC,aAAa,GAAGA;QACvB;QAEA,IAAIpC,SAAS;YACX,qEAAqE;YACrE,mEAAmE;YACnE,2DAA2D;YAC3D,EAAE;YACF,qFAAqF;YACrF,EAAE;YACF,uEAAuE;YACvE,kCAAkC;YAClC,IAAIqB,MAAMC,OAAO,CAACtB,UAAU;gBAC1B,0EAA0E;gBAC1E,oEAAoE;gBACpE,sEAAsE;gBACtE,qDAAqD;gBACrD,IAAK,IAAIwE,IAAI,GAAGA,IAAIxE,QAAQuE,MAAM,EAAEC,KAAK,EAAG;oBAC1C,2DAA2D;oBAC3D,IAAI,CAACC,SAAS,CAACzE,OAAO,CAACwE,EAAE,EAAYxE,OAAO,CAACwE,IAAI,EAAE;gBACrD;YACF,OAAO;gBACL,KAAK,MAAM,CAACvD,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACpB,SAAU;oBAClD,wBAAwB;oBACxB,IAAI,OAAOkB,UAAU,aAAa;oBAElC,IAAI,CAACuD,SAAS,CAACxD,KAAKC;gBACtB;YACF;QACF;QAEA,IAAI,CAACsB,UAAU,GAAGA;QAClB,IAAI,CAACF,WAAW,GAAG;QACnB,IAAI,CAACO,kBAAkB,oBAAvB,IAAI,CAACA,kBAAkB,MAAvB,IAAI;QAEJ,OAAO,IAAI;IACb;IAEO6B,UAAUrB,IAAY,EAAW;QACtC,OAAO,IAAI,CAACrD,OAAO,CAAC2E,GAAG,CAACtB;IAC1B;IAEOuB,UAAUvB,IAAY,EAAsB;QACjD,OAAO,IAAI,CAACrD,OAAO,CAACQ,GAAG,CAAC6C,SAASzC;IACnC;IAEOiE,aAAkC;QACvC,OAAOlF,0BAA0B,IAAI,CAACK,OAAO;IAC/C;IAEO8E,iBAA2B;QAChC,OAAOzD,MAAM0C,IAAI,CAAC,IAAI,CAAC/D,OAAO,CAAC+E,IAAI;IACrC;IAEON,UAAUpB,IAAY,EAAEnC,KAAyB,EAAE;QACxD,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxB,qEAAqE;YACrE,uEAAuE;YACvE,IAAI,CAAClB,OAAO,CAACgF,MAAM,CAAC3B;YAEpB,KAAK,MAAME,KAAKrC,MAAO;gBACrB,IAAI,CAAClB,OAAO,CAACwD,MAAM,CAACH,MAAME;YAC5B;QACF,OAAO,IAAI,OAAOrC,UAAU,UAAU;YACpC,IAAI,CAAClB,OAAO,CAACiF,GAAG,CAAC5B,MAAMnC,MAAMgE,QAAQ;QACvC,OAAO;YACL,IAAI,CAAClF,OAAO,CAACiF,GAAG,CAAC5B,MAAMnC;QACzB;QAEA,OAAO,IAAI;IACb;IAEOiE,aAAa9B,IAAY,EAAQ;QACtC,IAAI,CAACrD,OAAO,CAACgF,MAAM,CAAC3B;IACtB;IAEO+B,eAAqB;IAC1B,uEAAuE;IACvE,cAAc;IAChB;IAEA,wEAAwE;IACxE,oBAAoB;IAEpB,IAAWC,sBAA+B;QACxC,MAAM,qBAAoC,CAApC,IAAI1E,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEO2E,kBAAkB;QACvB,MAAM,qBAAoC,CAApC,IAAI3E,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEA,IAAW4E,MAAuB;QAChC,MAAM,qBAAoC,CAApC,IAAI5E,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEO6E,eAAe;QACpB,MAAM,qBAAoC,CAApC,IAAI7E,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEO8E,eAAqB;QAC1B,MAAM,qBAAoC,CAApC,IAAI9E,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEO+E,gBAAsB;QAC3B,MAAM,qBAAoC,CAApC,IAAI/E,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEOgF,kBAAwB;QAC7B,MAAM,qBAAoC,CAApC,IAAIhF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEA,IAAWiF,YAAqB;QAC9B,MAAM,qBAAoC,CAApC,IAAIjF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEA,IAAWkF,kBAA2B;QACpC,MAAM,qBAAoC,CAApC,IAAIlF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEA,IAAWmF,kBAA2B;QACpC,MAAM,qBAAoC,CAApC,IAAInF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEA,IAAWoF,8BAAuC;QAChD,MAAM,qBAAoC,CAApC,IAAIpF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEA,IAAWqF,WAAoB;QAC7B,MAAM,qBAAoC,CAApC,IAAIrF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEOqB,aAAmB;QACxB,MAAM,qBAAoC,CAApC,IAAIrB,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEOsF,cAAoB;QACzB,MAAM,qBAAoC,CAApC,IAAItF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;IAEOuF,aAAmB;QACxB,MAAM,qBAAoC,CAApC,IAAIvF,MAAM,4BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmC;IAC3C;AACF;AAWA,OAAO,SAASwF,2BAA2B,EACzCpG,GAAG,EACHC,UAAU,CAAC,CAAC,EACZC,SAAS,KAAK,EACdY,YAAY,EACZsC,SAAS,EACTjD,SAAS,IAAI,EACgB;IAC7B,OAAO;QACLqF,KAAK,IAAI3F,cAAc;YACrBG;YACAC;YACAC;YACAC;YACAC,UAAUU;QACZ;QACAsB,KAAK,IAAIF,eAAe;YAAE/B;YAAQiD;QAAU;IAC9C;AACF", "ignoreList": [0]}