{"version": 3, "sources": ["../../../src/shared/lib/router-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n"], "names": ["React", "RouterContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAGzB,OAAO,MAAMC,gBAAgBD,MAAME,aAAa,CAAoB,MAAK;AAEzE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,cAAcK,WAAW,GAAG;AAC9B", "ignoreList": [0]}