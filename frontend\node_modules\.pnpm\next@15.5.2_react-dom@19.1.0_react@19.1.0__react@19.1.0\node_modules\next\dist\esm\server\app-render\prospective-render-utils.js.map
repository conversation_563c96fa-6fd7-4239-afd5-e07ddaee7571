{"version": 3, "sources": ["../../../src/server/app-render/prospective-render-utils.ts"], "sourcesContent": ["import { getDigestForWellKnownError } from './create-error-handler'\nimport { isReactLargeShellError } from './react-large-shell-error'\n\nexport function printDebugThrownValueForProspectiveRender(\n  thrownValue: unknown,\n  route: string\n) {\n  // We don't need to print well-known Next.js errors.\n  if (getDigestForWellKnownError(thrownValue)) {\n    return\n  }\n\n  if (isReactLargeShellError(thrownValue)) {\n    // TODO: Aggregate\n    console.error(thrownValue)\n    return undefined\n  }\n\n  let message: undefined | string\n  if (\n    typeof thrownValue === 'object' &&\n    thrownValue !== null &&\n    typeof (thrownValue as any).message === 'string'\n  ) {\n    message = (thrownValue as any).message\n    if (typeof (thrownValue as any).stack === 'string') {\n      const originalErrorStack: string = (thrownValue as any).stack\n      const stackStart = originalErrorStack.indexOf('\\n')\n      if (stackStart > -1) {\n        const error = new Error(\n          `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled.\n          \nOriginal Error: ${message}`\n        )\n        error.stack =\n          'Error: ' + error.message + originalErrorStack.slice(stackStart)\n        console.error(error)\n        return\n      }\n    }\n  } else if (typeof thrownValue === 'string') {\n    message = thrownValue\n  }\n\n  if (message) {\n    console.error(`Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. No stack was provided.\n          \nOriginal Message: ${message}`)\n    return\n  }\n\n  console.error(\n    `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. The thrown value is logged just following this message`\n  )\n  console.error(thrownValue)\n  return\n}\n"], "names": ["getDigestForWellKnownError", "isReactLargeShellError", "printDebugThrownValueForProspectiveRender", "thrownValue", "route", "console", "error", "undefined", "message", "stack", "originalErrorStack", "stackStart", "indexOf", "Error", "slice"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,yBAAwB;AACnE,SAASC,sBAAsB,QAAQ,4BAA2B;AAElE,OAAO,SAASC,0CACdC,WAAoB,EACpBC,KAAa;IAEb,oDAAoD;IACpD,IAAIJ,2BAA2BG,cAAc;QAC3C;IACF;IAEA,IAAIF,uBAAuBE,cAAc;QACvC,kBAAkB;QAClBE,QAAQC,KAAK,CAACH;QACd,OAAOI;IACT;IAEA,IAAIC;IACJ,IACE,OAAOL,gBAAgB,YACvBA,gBAAgB,QAChB,OAAO,AAACA,YAAoBK,OAAO,KAAK,UACxC;QACAA,UAAU,AAACL,YAAoBK,OAAO;QACtC,IAAI,OAAO,AAACL,YAAoBM,KAAK,KAAK,UAAU;YAClD,MAAMC,qBAA6B,AAACP,YAAoBM,KAAK;YAC7D,MAAME,aAAaD,mBAAmBE,OAAO,CAAC;YAC9C,IAAID,aAAa,CAAC,GAAG;gBACnB,MAAML,QAAQ,qBAIb,CAJa,IAAIO,MAChB,CAAC,MAAM,EAAET,MAAM;;gBAET,EAAEI,SAAS,GAHL,qBAAA;2BAAA;gCAAA;kCAAA;gBAId;gBACAF,MAAMG,KAAK,GACT,YAAYH,MAAME,OAAO,GAAGE,mBAAmBI,KAAK,CAACH;gBACvDN,QAAQC,KAAK,CAACA;gBACd;YACF;QACF;IACF,OAAO,IAAI,OAAOH,gBAAgB,UAAU;QAC1CK,UAAUL;IACZ;IAEA,IAAIK,SAAS;QACXH,QAAQC,KAAK,CAAC,CAAC,MAAM,EAAEF,MAAM;;kBAEf,EAAEI,SAAS;QACzB;IACF;IAEAH,QAAQC,KAAK,CACX,CAAC,MAAM,EAAEF,MAAM,wOAAwO,CAAC;IAE1PC,QAAQC,KAAK,CAACH;IACd;AACF", "ignoreList": [0]}