{"version": 3, "sources": ["../../../src/server/dev/node-stack-frames.ts"], "sourcesContent": ["import { parse } from 'next/dist/compiled/stacktrace-parser'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  decorateServerError,\n  type ErrorSourceType,\n} from '../../shared/lib/error-source'\n\nfunction getFilesystemFrame(frame: StackFrame): StackFrame {\n  const f: StackFrame = { ...frame }\n\n  if (typeof f.file === 'string') {\n    if (\n      // Posix:\n      f.file.startsWith('/') ||\n      // Win32:\n      /^[a-z]:\\\\/i.test(f.file) ||\n      // Win32 UNC:\n      f.file.startsWith('\\\\\\\\')\n    ) {\n      f.file = `file://${f.file}`\n    }\n  }\n\n  return f\n}\n\nexport function getServerError(error: Error, type: ErrorSourceType): Error {\n  if (error.name === 'TurbopackInternalError') {\n    // If this is an internal Turbopack error we shouldn't show internal details\n    // to the user. These are written to a log file instead.\n    const turbopackInternalError = new Error(\n      'An unexpected Turbopack error occurred. Please see the output of `next dev` for more details.'\n    )\n    decorateServerError(turbopackInternalError, type)\n    return turbopackInternalError\n  }\n\n  let n: Error\n  try {\n    throw new Error(error.message)\n  } catch (e) {\n    n = e as Error\n  }\n\n  n.name = error.name\n  try {\n    n.stack = `${n.toString()}\\n${parse(error.stack!)\n      .map(getFilesystemFrame)\n      .map((f) => {\n        let str = `    at ${f.methodName}`\n        if (f.file) {\n          let loc = f.file\n          if (f.lineNumber) {\n            loc += `:${f.lineNumber}`\n            if (f.column) {\n              loc += `:${f.column}`\n            }\n          }\n          str += ` (${loc})`\n        }\n        return str\n      })\n      .join('\\n')}`\n  } catch {\n    n.stack = error.stack\n  }\n\n  decorateServerError(n, type)\n  return n\n}\n"], "names": ["parse", "decorateServerError", "getFilesystemFrame", "frame", "f", "file", "startsWith", "test", "getServerError", "error", "type", "name", "turbopackInternalError", "Error", "n", "message", "e", "stack", "toString", "map", "str", "methodName", "loc", "lineNumber", "column", "join"], "mappings": "AAAA,SAASA,KAAK,QAAQ,uCAAsC;AAE5D,SACEC,mBAAmB,QAEd,gCAA+B;AAEtC,SAASC,mBAAmBC,KAAiB;IAC3C,MAAMC,IAAgB;QAAE,GAAGD,KAAK;IAAC;IAEjC,IAAI,OAAOC,EAAEC,IAAI,KAAK,UAAU;QAC9B,IACE,SAAS;QACTD,EAAEC,IAAI,CAACC,UAAU,CAAC,QAClB,SAAS;QACT,aAAaC,IAAI,CAACH,EAAEC,IAAI,KACxB,aAAa;QACbD,EAAEC,IAAI,CAACC,UAAU,CAAC,SAClB;YACAF,EAAEC,IAAI,GAAG,CAAC,OAAO,EAAED,EAAEC,IAAI,EAAE;QAC7B;IACF;IAEA,OAAOD;AACT;AAEA,OAAO,SAASI,eAAeC,KAAY,EAAEC,IAAqB;IAChE,IAAID,MAAME,IAAI,KAAK,0BAA0B;QAC3C,4EAA4E;QAC5E,wDAAwD;QACxD,MAAMC,yBAAyB,qBAE9B,CAF8B,IAAIC,MACjC,kGAD6B,qBAAA;mBAAA;wBAAA;0BAAA;QAE/B;QACAZ,oBAAoBW,wBAAwBF;QAC5C,OAAOE;IACT;IAEA,IAAIE;IACJ,IAAI;QACF,MAAM,qBAAwB,CAAxB,IAAID,MAAMJ,MAAMM,OAAO,GAAvB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B,EAAE,OAAOC,GAAG;QACVF,IAAIE;IACN;IAEAF,EAAEH,IAAI,GAAGF,MAAME,IAAI;IACnB,IAAI;QACFG,EAAEG,KAAK,GAAG,GAAGH,EAAEI,QAAQ,GAAG,EAAE,EAAElB,MAAMS,MAAMQ,KAAK,EAC5CE,GAAG,CAACjB,oBACJiB,GAAG,CAAC,CAACf;YACJ,IAAIgB,MAAM,CAAC,OAAO,EAAEhB,EAAEiB,UAAU,EAAE;YAClC,IAAIjB,EAAEC,IAAI,EAAE;gBACV,IAAIiB,MAAMlB,EAAEC,IAAI;gBAChB,IAAID,EAAEmB,UAAU,EAAE;oBAChBD,OAAO,CAAC,CAAC,EAAElB,EAAEmB,UAAU,EAAE;oBACzB,IAAInB,EAAEoB,MAAM,EAAE;wBACZF,OAAO,CAAC,CAAC,EAAElB,EAAEoB,MAAM,EAAE;oBACvB;gBACF;gBACAJ,OAAO,CAAC,EAAE,EAAEE,IAAI,CAAC,CAAC;YACpB;YACA,OAAOF;QACT,GACCK,IAAI,CAAC,OAAO;IACjB,EAAE,OAAM;QACNX,EAAEG,KAAK,GAAGR,MAAMQ,KAAK;IACvB;IAEAhB,oBAAoBa,GAAGJ;IACvB,OAAOI;AACT", "ignoreList": [0]}